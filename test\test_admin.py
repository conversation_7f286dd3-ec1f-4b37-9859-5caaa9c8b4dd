import requests
import json

# Test admin login
def test_admin_login():
    url = "http://localhost:5000/api/admin/login"
    data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("Admin login successful!")
            print(f"Token: {result.get('token', 'No token')}")
            print(f"Admin: {result.get('admin', 'No admin data')}")
            return result.get('token')
        else:
            print("Admin login failed!")
            return None
            
    except Exception as e:
        print(f"Error: {str(e)}")
        return None

# Test admin dashboard
def test_admin_dashboard(token):
    if not token:
        print("No token available for dashboard test")
        return
        
    url = "http://localhost:5000/api/admin/dashboard"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers)
        print(f"\nDashboard Status Code: {response.status_code}")
        print(f"Dashboard Response: {response.text}")
        
    except Exception as e:
        print(f"Dashboard Error: {str(e)}")

if __name__ == "__main__":
    print("Testing Admin Panel...")
    token = test_admin_login()
    test_admin_dashboard(token)
