#!/usr/bin/env python3
"""
Add Sample Products Script for Allora E-commerce Platform
=========================================================

This script adds sample sustainable products to the database for testing
the new home page design and functionality.

Author: Allora Development Team
Date: 2025-07-10
"""

import sys
import os
from datetime import datetime, timedelta
import random

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Product
from decimal import Decimal

def create_sample_products():
    """Create sample sustainable products for testing"""
    
    sample_products = [
        {
            'name': 'Organic Cotton T-Shirt',
            'description': 'Soft, breathable organic cotton t-shirt made from 100% certified organic cotton. Perfect for everyday wear with a commitment to sustainability.',
            'price': Decimal('899.00'),
            'category': 'Clothing',
            'stock_quantity': 50,
            'image': 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',
            'sustainability_score': 9,
            'average_rating': 4.5,
            'total_reviews': 23,
            'carbon_neutral': True,
            'organic': True
        },
        {
            'name': 'Bamboo Water Bottle',
            'description': 'Eco-friendly bamboo water bottle with stainless steel interior. Keeps drinks cold for 24 hours and hot for 12 hours.',
            'price': Decimal('1299.00'),
            'category': 'Home & Kitchen',
            'stock_quantity': 75,
            'image': 'https://images.unsplash.com/photo-*************-7111542de6e8?w=400&h=400&fit=crop',
            'sustainability_score': 8,
            'average_rating': 4.7,
            'total_reviews': 45,
            'carbon_neutral': True,
            'recycled_materials': True
        },
        {
            'name': 'Solar Power Bank',
            'description': 'Portable solar power bank with 20,000mAh capacity. Charge your devices using clean solar energy anywhere you go.',
            'price': Decimal('2499.00'),
            'category': 'Electronics',
            'stock_quantity': 30,
            'image': 'https://images.unsplash.com/photo-*************-d5365f9ff1c5?w=400&h=400&fit=crop',
            'sustainability_score': 9,
            'average_rating': 4.3,
            'total_reviews': 67,
            'carbon_neutral': False,
            'organic': False
        },
        {
            'name': 'Recycled Plastic Backpack',
            'description': 'Durable backpack made from 100% recycled plastic bottles. Features multiple compartments and water-resistant coating.',
            'price': Decimal('1899.00'),
            'category': 'Accessories',
            'stock_quantity': 40,
            'image': 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=400&h=400&fit=crop',
            'sustainability_score': 8,
            'average_rating': 4.6,
            'total_reviews': 34,
            'carbon_neutral': True,
            'recycled_materials': True
        },
        {
            'name': 'Fair Trade Coffee Beans',
            'description': 'Premium arabica coffee beans sourced directly from fair trade certified farms. Rich, smooth flavor with notes of chocolate and caramel.',
            'price': Decimal('699.00'),
            'category': 'Food & Beverages',
            'stock_quantity': 100,
            'image': 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400&h=400&fit=crop',
            'sustainability_score': 9,
            'average_rating': 4.8,
            'total_reviews': 89,
            'fair_trade': True,
            'organic': True
        },
        {
            'name': 'Hemp Fiber Yoga Mat',
            'description': 'Natural yoga mat made from sustainable hemp fiber. Non-slip surface with excellent grip and cushioning for all yoga practices.',
            'price': Decimal('1599.00'),
            'category': 'Sports & Fitness',
            'stock_quantity': 25,
            'image': 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=400&fit=crop',
            'sustainability_score': 8,
            'average_rating': 4.4,
            'total_reviews': 56,
            'organic': True,
            'carbon_neutral': True
        },
        {
            'name': 'Biodegradable Phone Case',
            'description': 'Protective phone case made from biodegradable materials. Breaks down naturally at end of life while providing excellent protection.',
            'price': Decimal('799.00'),
            'category': 'Electronics',
            'stock_quantity': 60,
            'image': 'https://images.unsplash.com/photo-1556656793-08538906a9f8?w=400&h=400&fit=crop',
            'sustainability_score': 9,
            'average_rating': 4.2,
            'total_reviews': 78,
            'carbon_neutral': True,
            'organic': False
        },
        {
            'name': 'Organic Skincare Set',
            'description': 'Complete skincare routine with organic ingredients. Includes cleanser, toner, serum, and moisturizer. Cruelty-free and vegan.',
            'price': Decimal('2299.00'),
            'category': 'Beauty & Personal Care',
            'stock_quantity': 35,
            'image': 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=400&fit=crop',
            'sustainability_score': 9,
            'average_rating': 4.7,
            'total_reviews': 92,
            'organic': True,
            'fair_trade': True
        },
        {
            'name': 'Sustainable Wooden Desk',
            'description': 'Minimalist desk made from sustainably sourced wood. Perfect for home office with built-in cable management and storage.',
            'price': Decimal('8999.00'),
            'category': 'Furniture',
            'stock_quantity': 15,
            'image': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
            'sustainability_score': 8,
            'average_rating': 4.9,
            'total_reviews': 12,
            'carbon_neutral': True,
            'organic': False
        },
        {
            'name': 'Eco-Friendly Cleaning Kit',
            'description': 'Complete cleaning kit with plant-based, non-toxic cleaners. Safe for family and pets while being tough on dirt and grime.',
            'price': Decimal('1199.00'),
            'category': 'Home & Kitchen',
            'stock_quantity': 80,
            'image': 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400&h=400&fit=crop',
            'sustainability_score': 9,
            'average_rating': 4.5,
            'total_reviews': 67,
            'organic': True,
            'carbon_neutral': True
        }
    ]
    
    with app.app_context():
        try:
            # Check if products already exist
            existing_count = Product.query.count()
            if existing_count > 0:
                print(f"Database already has {existing_count} products. Skipping sample data creation.")
                return
            
            print("Creating sample sustainable products...")
            
            for i, product_data in enumerate(sample_products):
                # Add some variation to creation dates (last 30 days)
                created_date = datetime.utcnow() - timedelta(days=random.randint(1, 30))
                
                product = Product(
                    name=product_data['name'],
                    description=product_data['description'],
                    price=product_data['price'],
                    category=product_data['category'],
                    stock_quantity=product_data['stock_quantity'],
                    image=product_data['image'],
                    sustainability_score=product_data['sustainability_score'],
                    average_rating=product_data['average_rating'],
                    total_reviews=product_data['total_reviews'],
                    created_at=created_date,
                    updated_at=created_date
                )
                
                # Add sustainable attributes if they exist in the Product model
                for attr in ['carbon_neutral', 'organic', 'fair_trade', 'recycled_materials']:
                    if attr in product_data and hasattr(product, attr):
                        setattr(product, attr, product_data[attr])
                
                db.session.add(product)
                print(f"✅ Added: {product_data['name']}")
            
            db.session.commit()
            print(f"\n🎉 Successfully created {len(sample_products)} sample products!")
            print("✅ Database is now ready for testing the new home page design.")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error creating sample products: {str(e)}")
            return False
    
    return True

if __name__ == '__main__':
    print("🌱 Allora Sample Products Creator")
    print("=" * 50)
    
    success = create_sample_products()
    
    if success:
        print("\n🚀 Sample products created successfully!")
        print("You can now test the new home page with real data.")
    else:
        print("\n❌ Failed to create sample products.")
        print("Please check the error messages above.")
