#!/usr/bin/env python3
"""
Comprehensive Feature-Database Mapping Analysis for Allora E-commerce
Verifies that all project features have proper database implementation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
import logging
from sqlalchemy import inspect, text
from datetime import datetime
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeatureDatabaseMapper:
    def __init__(self):
        self.missing_features = []
        self.implemented_features = []
        self.partial_features = []
        
    def log_feature(self, category, feature_name, status, details=""):
        """Log feature implementation status"""
        feature_info = {
            'category': category,
            'feature': feature_name,
            'status': status,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        
        if status == "IMPLEMENTED":
            self.implemented_features.append(feature_info)
            print(f"✅ {category}: {feature_name} - {details}")
        elif status == "PARTIAL":
            self.partial_features.append(feature_info)
            print(f"⚠️  {category}: {feature_name} - {details}")
        else:
            self.missing_features.append(feature_info)
            print(f"❌ {category}: {feature_name} - {details}")

    def check_core_ecommerce_features(self):
        """Check core e-commerce functionality database support"""
        print("\n" + "="*80)
        print("🛒 CORE E-COMMERCE FEATURES")
        print("="*80)
        
        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            # 1. User Management
            if 'user' in tables or 'users' in tables:
                # Check user table structure
                user_table = 'users' if 'users' in tables else 'user'
                columns = [col['name'] for col in inspector.get_columns(user_table)]
                
                required_user_fields = ['id', 'username', 'email', 'password']
                missing_fields = [field for field in required_user_fields if field not in columns]
                
                if not missing_fields:
                    self.log_feature("USER_MANAGEMENT", "User Registration/Login", "IMPLEMENTED", 
                                   f"Table: {user_table}, Fields: {len(columns)}")
                else:
                    self.log_feature("USER_MANAGEMENT", "User Registration/Login", "PARTIAL", 
                                   f"Missing fields: {missing_fields}")
            else:
                self.log_feature("USER_MANAGEMENT", "User Registration/Login", "MISSING", "No user table found")
            
            # 2. Product Management
            if 'product' in tables or 'products' in tables:
                product_table = 'products' if 'products' in tables else 'product'
                columns = [col['name'] for col in inspector.get_columns(product_table)]
                
                required_product_fields = ['id', 'name', 'price', 'stock_quantity']
                missing_fields = [field for field in required_product_fields if field not in columns]
                
                if not missing_fields:
                    # Check for advanced product features
                    advanced_fields = ['sustainability_score', 'seller_id', 'category', 'brand']
                    advanced_present = [field for field in advanced_fields if field in columns]
                    
                    self.log_feature("PRODUCT_MANAGEMENT", "Product Catalog", "IMPLEMENTED", 
                                   f"Table: {product_table}, Advanced fields: {len(advanced_present)}")
                else:
                    self.log_feature("PRODUCT_MANAGEMENT", "Product Catalog", "PARTIAL", 
                                   f"Missing fields: {missing_fields}")
            else:
                self.log_feature("PRODUCT_MANAGEMENT", "Product Catalog", "MISSING", "No product table found")
            
            # 3. Shopping Cart
            if 'cart_item' in tables:
                columns = [col['name'] for col in inspector.get_columns('cart_item')]
                required_fields = ['id', 'user_id', 'product_id', 'quantity']
                missing_fields = [field for field in required_fields if field not in columns]
                
                if not missing_fields:
                    # Check for guest cart support
                    guest_support = 'guest_session_id' in columns
                    self.log_feature("SHOPPING_CART", "Shopping Cart", "IMPLEMENTED", 
                                   f"Guest support: {guest_support}")
                else:
                    self.log_feature("SHOPPING_CART", "Shopping Cart", "PARTIAL", 
                                   f"Missing fields: {missing_fields}")
            else:
                self.log_feature("SHOPPING_CART", "Shopping Cart", "MISSING", "No cart_item table found")
            
            # 4. Order Management
            if 'orders' in tables or 'order' in tables:
                order_table = 'orders' if 'orders' in tables else 'order'
                columns = [col['name'] for col in inspector.get_columns(order_table)]
                
                # Check for order items
                if 'order_item' in tables:
                    self.log_feature("ORDER_MANAGEMENT", "Order Processing", "IMPLEMENTED", 
                                   f"Tables: {order_table}, order_item")
                else:
                    self.log_feature("ORDER_MANAGEMENT", "Order Processing", "PARTIAL", 
                                   "Missing order_item table")
            else:
                self.log_feature("ORDER_MANAGEMENT", "Order Processing", "MISSING", "No order table found")

    def check_advanced_features(self):
        """Check advanced e-commerce features"""
        print("\n" + "="*80)
        print("🚀 ADVANCED E-COMMERCE FEATURES")
        print("="*80)
        
        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            # 1. Multi-vendor Marketplace
            if 'seller' in tables or 'sellers' in tables:
                seller_table = 'sellers' if 'sellers' in tables else 'seller'
                
                # Check for seller store
                if 'seller_store' in tables:
                    self.log_feature("MULTI_VENDOR", "Seller Management", "IMPLEMENTED", 
                                   f"Tables: {seller_table}, seller_store")
                else:
                    self.log_feature("MULTI_VENDOR", "Seller Management", "PARTIAL", 
                                   "Missing seller_store table")
                
                # Check for commission system
                if 'seller_commission' in tables and 'seller_payout' in tables:
                    self.log_feature("MULTI_VENDOR", "Commission System", "IMPLEMENTED", 
                                   "Tables: seller_commission, seller_payout")
                else:
                    self.log_feature("MULTI_VENDOR", "Commission System", "MISSING", 
                                   "Missing commission/payout tables")
            else:
                self.log_feature("MULTI_VENDOR", "Seller Management", "MISSING", "No seller table found")
            
            # 2. Payment System
            payment_tables = ['payment_gateway', 'payment_transaction', 'payment_method']
            payment_implemented = all(table in tables for table in payment_tables)
            
            if payment_implemented:
                self.log_feature("PAYMENT_SYSTEM", "Payment Processing", "IMPLEMENTED", 
                               f"Tables: {', '.join(payment_tables)}")
            else:
                missing_payment = [table for table in payment_tables if table not in tables]
                self.log_feature("PAYMENT_SYSTEM", "Payment Processing", "PARTIAL", 
                               f"Missing: {missing_payment}")
            
            # 3. Shipping & Fulfillment
            shipping_tables = ['shipping_carriers', 'shipments', 'tracking_events']
            shipping_implemented = all(table in tables for table in shipping_tables)
            
            if shipping_implemented:
                self.log_feature("SHIPPING", "Order Fulfillment", "IMPLEMENTED", 
                               f"Tables: {', '.join(shipping_tables)}")
            else:
                missing_shipping = [table for table in shipping_tables if table not in tables]
                self.log_feature("SHIPPING", "Order Fulfillment", "PARTIAL", 
                               f"Missing: {missing_shipping}")
            
            # 4. Inventory Management
            inventory_tables = ['inventory_log', 'channel_inventory', 'inventory_sync_log']
            inventory_implemented = all(table in tables for table in inventory_tables)
            
            if inventory_implemented:
                self.log_feature("INVENTORY", "Inventory Management", "IMPLEMENTED", 
                               f"Tables: {', '.join(inventory_tables)}")
            else:
                missing_inventory = [table for table in inventory_tables if table not in tables]
                self.log_feature("INVENTORY", "Inventory Management", "PARTIAL", 
                               f"Missing: {missing_inventory}")

    def check_customer_features(self):
        """Check customer-facing features"""
        print("\n" + "="*80)
        print("👥 CUSTOMER FEATURES")
        print("="*80)
        
        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            # 1. Wishlist
            if 'wishlist' in tables:
                self.log_feature("CUSTOMER", "Wishlist", "IMPLEMENTED", "Table: wishlist")
            else:
                self.log_feature("CUSTOMER", "Wishlist", "MISSING", "No wishlist table")
            
            # 2. Product Reviews
            if 'product_review' in tables:
                self.log_feature("CUSTOMER", "Product Reviews", "IMPLEMENTED", "Table: product_review")
            else:
                self.log_feature("CUSTOMER", "Product Reviews", "MISSING", "No product_review table")
            
            # 3. User Addresses
            if 'user_address' in tables:
                self.log_feature("CUSTOMER", "Address Management", "IMPLEMENTED", "Table: user_address")
            else:
                self.log_feature("CUSTOMER", "Address Management", "MISSING", "No user_address table")
            
            # 4. Recently Viewed
            if 'recently_viewed' in tables:
                self.log_feature("CUSTOMER", "Recently Viewed", "IMPLEMENTED", "Table: recently_viewed")
            else:
                self.log_feature("CUSTOMER", "Recently Viewed", "MISSING", "No recently_viewed table")
            
            # 5. Product Comparison
            if 'product_comparison' in tables:
                self.log_feature("CUSTOMER", "Product Comparison", "IMPLEMENTED", "Table: product_comparison")
            else:
                self.log_feature("CUSTOMER", "Product Comparison", "MISSING", "No product_comparison table")

    def check_marketing_features(self):
        """Check marketing and promotional features"""
        print("\n" + "="*80)
        print("📢 MARKETING & PROMOTIONS")
        print("="*80)
        
        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            # 1. Coupons & Discounts
            if 'coupon' in tables and 'coupon_usage' in tables:
                self.log_feature("MARKETING", "Coupon System", "IMPLEMENTED", 
                               "Tables: coupon, coupon_usage")
            else:
                self.log_feature("MARKETING", "Coupon System", "PARTIAL", 
                               "Missing coupon tables")
            
            # 2. Newsletter
            if 'newsletter_subscription' in tables:
                self.log_feature("MARKETING", "Newsletter", "IMPLEMENTED", 
                               "Table: newsletter_subscription")
            else:
                self.log_feature("MARKETING", "Newsletter", "MISSING", "No newsletter table")
            
            # 3. Abandoned Cart Recovery
            if 'abandoned_cart' in tables:
                self.log_feature("MARKETING", "Cart Abandonment", "IMPLEMENTED", 
                               "Table: abandoned_cart")
            else:
                self.log_feature("MARKETING", "Cart Abandonment", "MISSING", "No abandoned_cart table")
            
            # 4. Email Notifications
            if 'email_notification' in tables:
                self.log_feature("MARKETING", "Email Notifications", "IMPLEMENTED", 
                               "Table: email_notification")
            else:
                self.log_feature("MARKETING", "Email Notifications", "MISSING", "No email_notification table")

    def check_analytics_features(self):
        """Check analytics and tracking features"""
        print("\n" + "="*80)
        print("📊 ANALYTICS & TRACKING")
        print("="*80)

        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()

            # 1. Search Analytics
            if 'search_analytics' in tables:
                self.log_feature("ANALYTICS", "Search Analytics", "IMPLEMENTED",
                               "Table: search_analytics")
            else:
                self.log_feature("ANALYTICS", "Search Analytics", "MISSING", "No search_analytics table")

            # 2. User Behavior Tracking
            behavior_tables = ['user_interaction_logs', 'user_behavior_profiles', 'user_sessions']
            behavior_implemented = all(table in tables for table in behavior_tables)

            if behavior_implemented:
                self.log_feature("ANALYTICS", "User Behavior Tracking", "IMPLEMENTED",
                               f"Tables: {', '.join(behavior_tables)}")
            else:
                missing_behavior = [table for table in behavior_tables if table not in tables]
                self.log_feature("ANALYTICS", "User Behavior Tracking", "PARTIAL",
                               f"Missing: {missing_behavior}")

            # 3. Visual Search Analytics
            if 'visual_search_analytics' in tables:
                self.log_feature("ANALYTICS", "Visual Search", "IMPLEMENTED",
                               "Table: visual_search_analytics")
            else:
                self.log_feature("ANALYTICS", "Visual Search", "MISSING", "No visual_search_analytics table")

            # 4. Sales Analytics
            if 'sales' in tables and 'price_history' in tables:
                self.log_feature("ANALYTICS", "Sales Analytics", "IMPLEMENTED",
                               "Tables: sales, price_history")
            else:
                self.log_feature("ANALYTICS", "Sales Analytics", "PARTIAL",
                               "Missing sales/price_history tables")

    def check_advanced_system_features(self):
        """Check advanced system features like RMA, Support, Admin, etc."""
        print("\n" + "="*80)
        print("🏢 ADVANCED SYSTEM FEATURES")
        print("="*80)

        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()

            # 1. RMA (Return Merchandise Authorization) System
            rma_tables = ['rma_request', 'rma_item', 'rma_timeline', 'rma_document', 'rma_approval']
            rma_implemented = all(table in tables for table in rma_tables)

            if rma_implemented:
                self.log_feature("RMA_SYSTEM", "Return Management", "IMPLEMENTED",
                               f"Tables: {', '.join(rma_tables)}")
            else:
                missing_rma = [table for table in rma_tables if table not in tables]
                self.log_feature("RMA_SYSTEM", "Return Management", "PARTIAL",
                               f"Missing: {missing_rma}")

            # 2. Customer Support System
            support_tables = ['support_ticket', 'support_message', 'support_attachment']
            support_implemented = all(table in tables for table in support_tables)

            if support_implemented:
                self.log_feature("SUPPORT_SYSTEM", "Customer Support", "IMPLEMENTED",
                               f"Tables: {', '.join(support_tables)}")
            else:
                missing_support = [table for table in support_tables if table not in tables]
                self.log_feature("SUPPORT_SYSTEM", "Customer Support", "PARTIAL",
                               f"Missing: {missing_support}")

            # 3. Admin Management System
            admin_tables = ['admin_user', 'admin_activity_log']
            admin_implemented = all(table in tables for table in admin_tables)

            if admin_implemented:
                self.log_feature("ADMIN_SYSTEM", "Admin Management", "IMPLEMENTED",
                               f"Tables: {', '.join(admin_tables)}")
            else:
                missing_admin = [table for table in admin_tables if table not in tables]
                self.log_feature("ADMIN_SYSTEM", "Admin Management", "PARTIAL",
                               f"Missing: {missing_admin}")

            # 4. Chat/Communication System
            chat_tables = ['chat_session', 'chat_message']
            chat_implemented = all(table in tables for table in chat_tables)

            if chat_implemented:
                self.log_feature("CHAT_SYSTEM", "Live Chat", "IMPLEMENTED",
                               f"Tables: {', '.join(chat_tables)}")
            else:
                missing_chat = [table for table in chat_tables if table not in tables]
                self.log_feature("CHAT_SYSTEM", "Live Chat", "PARTIAL",
                               f"Missing: {missing_chat}")

    def check_compliance_features(self):
        """Check compliance and security features"""
        print("\n" + "="*80)
        print("🔒 COMPLIANCE & SECURITY")
        print("="*80)

        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()

            # 1. Cookie Consent & GDPR
            cookie_tables = ['cookie_consent', 'cookie_consent_history', 'cookie_audit_log']
            cookie_implemented = all(table in tables for table in cookie_tables)

            if cookie_implemented:
                self.log_feature("COMPLIANCE", "Cookie Consent/GDPR", "IMPLEMENTED",
                               f"Tables: {', '.join(cookie_tables)}")
            else:
                missing_cookie = [table for table in cookie_tables if table not in tables]
                self.log_feature("COMPLIANCE", "Cookie Consent/GDPR", "PARTIAL",
                               f"Missing: {missing_cookie}")

            # 2. Data Export/Privacy
            if 'data_export_request' in tables:
                self.log_feature("COMPLIANCE", "Data Export/Privacy", "IMPLEMENTED",
                               "Table: data_export_request")
            else:
                self.log_feature("COMPLIANCE", "Data Export/Privacy", "MISSING",
                               "No data_export_request table")

            # 3. OAuth Integration
            oauth_tables = ['o_auth_provider', 'user_o_auth']
            oauth_implemented = all(table in tables for table in oauth_tables)

            if oauth_implemented:
                self.log_feature("SECURITY", "OAuth Integration", "IMPLEMENTED",
                               f"Tables: {', '.join(oauth_tables)}")
            else:
                missing_oauth = [table for table in oauth_tables if table not in tables]
                self.log_feature("SECURITY", "OAuth Integration", "PARTIAL",
                               f"Missing: {missing_oauth}")

    def check_community_features(self):
        """Check community and social features"""
        print("\n" + "="*80)
        print("👥 COMMUNITY & SOCIAL FEATURES")
        print("="*80)

        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()

            # 1. Community Posts System
            community_tables = ['community_post', 'post_comment', 'post_like', 'post_hashtag']
            community_implemented = all(table in tables for table in community_tables)

            if community_implemented:
                self.log_feature("COMMUNITY", "Community Posts", "IMPLEMENTED",
                               f"Tables: {', '.join(community_tables)}")
            else:
                missing_community = [table for table in community_tables if table not in tables]
                self.log_feature("COMMUNITY", "Community Posts", "PARTIAL",
                               f"Missing: {missing_community}")

            # 2. Hashtag System
            if 'hashtag' in tables:
                self.log_feature("COMMUNITY", "Hashtag System", "IMPLEMENTED",
                               "Table: hashtag")
            else:
                self.log_feature("COMMUNITY", "Hashtag System", "MISSING", "No hashtag table")

            # 3. Community Analytics
            if 'community_stats' in tables:
                self.log_feature("COMMUNITY", "Community Analytics", "IMPLEMENTED",
                               "Table: community_stats")
            else:
                self.log_feature("COMMUNITY", "Community Analytics", "MISSING", "No community_stats table")

            # 4. Community Insights
            if 'community_insight' in tables:
                self.log_feature("COMMUNITY", "Community Insights", "IMPLEMENTED",
                               "Table: community_insight")
            else:
                self.log_feature("COMMUNITY", "Community Insights", "MISSING", "No community_insight table")

def main():
    print("🚀 Starting Feature-Database Mapping Analysis...")
    print(f"⏰ Analysis started at: {datetime.now()}")
    
    mapper = FeatureDatabaseMapper()
    
    # Run all feature checks
    mapper.check_core_ecommerce_features()
    mapper.check_advanced_features()
    mapper.check_customer_features()
    mapper.check_marketing_features()
    mapper.check_analytics_features()
    mapper.check_advanced_system_features()
    mapper.check_compliance_features()
    mapper.check_community_features()
    
    # Generate summary report
    print(f"\n🎯 FEATURE IMPLEMENTATION SUMMARY")
    print("="*80)
    
    total_features = len(mapper.implemented_features) + len(mapper.partial_features) + len(mapper.missing_features)
    implemented_count = len(mapper.implemented_features)
    partial_count = len(mapper.partial_features)
    missing_count = len(mapper.missing_features)
    
    print(f"✅ Fully Implemented: {implemented_count}")
    print(f"⚠️  Partially Implemented: {partial_count}")
    print(f"❌ Missing: {missing_count}")
    print(f"📊 Implementation Rate: {(implemented_count/total_features)*100:.1f}%")
    
    if missing_count > 0:
        print(f"\n❌ MISSING FEATURES:")
        for feature in mapper.missing_features:
            print(f"  - {feature['category']}: {feature['feature']} - {feature['details']}")
    
    if partial_count > 0:
        print(f"\n⚠️  PARTIAL IMPLEMENTATIONS:")
        for feature in mapper.partial_features:
            print(f"  - {feature['category']}: {feature['feature']} - {feature['details']}")
    
    # Save detailed report
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_features': total_features,
            'implemented': implemented_count,
            'partial': partial_count,
            'missing': missing_count,
            'implementation_rate': (implemented_count/total_features)*100
        },
        'implemented_features': mapper.implemented_features,
        'partial_features': mapper.partial_features,
        'missing_features': mapper.missing_features
    }
    
    with open('feature_database_mapping_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: feature_database_mapping_report.json")
    print(f"⏰ Analysis completed at: {datetime.now()}")

if __name__ == '__main__':
    main()
