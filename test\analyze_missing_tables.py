#!/usr/bin/env python3
"""
Analyze Missing Database Tables Script
=====================================

This script analyzes the missing database tables that don't have 
corresponding models in app.py.

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import sys
from dotenv import load_dotenv
from sqlalchemy import inspect

# Load environment variables
load_dotenv()

def analyze_missing_tables():
    """Analyze the structure of missing database tables"""
    
    try:
        from app import app, db
        
        with app.app_context():
            inspector = inspect(db.engine)
            
            # All database tables
            all_db_tables = set(inspector.get_table_names())
            
            # Tables that have models (based on our previous test)
            tables_with_models = {
                'abandoned_cart', 'admin_activity_log', 'admin_user', 'availability_notification', 
                'banner', 'carrier_rates', 'cart_item', 'channel_inventory', 'community_insight', 
                'community_post', 'community_stats', 'content_page', 'cookie_audit_log', 
                'cookie_consent', 'cookie_consent_history', 'coupon', 'coupon_usage', 
                'data_export_request', 'email_notification', 'fulfillment_rules', 'guest_session', 
                'hashtag', 'inventory_conflict', 'inventory_log', 'inventory_sync_log', 'invoice', 
                'newsletter_subscription', 'o_auth_provider', 'orders', 'order_item', 
                'payment_gateway', 'payment_method', 'payment_transaction', 'post_comment', 
                'post_hashtag', 'post_like', 'price_history', 'products', 'product_comparison', 
                'product_image', 'product_review', 'product_variant', 'recently_viewed', 'refund', 
                'return_shipment', 'rma_approval', 'rma_configuration', 'rma_document', 'rma_item', 
                'rma_request', 'rma_rule', 'rma_stats', 'rma_timeline', 'sales', 'sales_channel', 
                'saved_cart', 'search_analytics', 'sellers', 'seller_commission', 'seller_payout', 
                'seller_store', 'shipments', 'shipping_carriers', 'shipping_method', 'shipping_zone', 
                'support_attachment', 'support_message', 'support_ticket', 'sync_queue', 'tax_rate', 
                'tracking_events', 'users', 'user_address', 'user_behavior_profiles', 
                'user_interaction_logs', 'user_o_auth', 'user_sessions', 'visual_search_analytics', 
                'wishlist'
            }
            
            # Find missing tables
            missing_tables = all_db_tables - tables_with_models
            
            print(f"📊 Total database tables: {len(all_db_tables)}")
            print(f"📊 Tables with models: {len(tables_with_models)}")
            print(f"📊 Missing tables: {len(missing_tables)}")
            print()
            
            print("❌ Missing tables that need models:")
            for table in sorted(missing_tables):
                print(f"  - {table}")
            print()
            
            print("🔍 Analyzing missing table structures:")
            print("=" * 60)
            
            for table_name in sorted(missing_tables):
                if inspector.has_table(table_name):
                    print(f"\n📋 Table: {table_name}")
                    print("-" * 30)
                    
                    columns = inspector.get_columns(table_name)
                    for col in columns:
                        col_type = str(col['type'])
                        nullable = 'NULL' if col['nullable'] else 'NOT NULL'
                        default_val = col.get('default')
                        default = f" DEFAULT {default_val}" if default_val else ""
                        print(f"  - {col['name']}: {col_type} {nullable}{default}")
                    
                    # Get foreign keys
                    fks = inspector.get_foreign_keys(table_name)
                    if fks:
                        print("  Foreign Keys:")
                        for fk in fks:
                            constrained = ', '.join(fk['constrained_columns'])
                            referred = ', '.join(fk['referred_columns'])
                            print(f"    - {constrained} -> {fk['referred_table']}.{referred}")
                    
                    # Get indexes
                    indexes = inspector.get_indexes(table_name)
                    if indexes:
                        print("  Indexes:")
                        for idx in indexes:
                            cols = ', '.join(idx['column_names'])
                            unique = " (UNIQUE)" if idx['unique'] else ""
                            print(f"    - {idx['name']}: {cols}{unique}")
                    
                    # Get primary key
                    pk = inspector.get_pk_constraint(table_name)
                    if pk and pk['constrained_columns']:
                        pk_cols = ', '.join(pk['constrained_columns'])
                        print(f"  Primary Key: {pk_cols}")
                        
    except Exception as e:
        print(f"❌ Error analyzing tables: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("🔍 Missing Database Tables Analysis")
    print("=" * 50)
    
    if analyze_missing_tables():
        print("\n✅ Analysis completed successfully!")
        return 0
    else:
        print("\n❌ Analysis failed.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
