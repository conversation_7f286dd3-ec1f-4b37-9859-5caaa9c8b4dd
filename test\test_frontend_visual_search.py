#!/usr/bin/env python3
"""
Frontend Visual Search Integration Test
======================================

This script tests the visual search API from the frontend perspective
to ensure the data flow is working correctly.
"""

import requests
import json
import os
from PIL import Image
import io

def create_test_image():
    """Create a simple test image"""
    # Create a simple colored image
    img = Image.new('RGB', (224, 224), color='blue')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    return img_bytes

def test_frontend_visual_search_flow():
    """Test the complete frontend visual search flow"""
    print("🔍 TESTING FRONTEND VISUAL SEARCH FLOW")
    print("=" * 60)
    
    try:
        # Step 1: Test the API endpoint that frontend uses
        print("Step 1: Testing /api/visual_search endpoint...")
        test_image = create_test_image()
        
        files = {'image': ('test.jpg', test_image, 'image/jpeg')}
        response = requests.post('http://localhost:5000/api/visual_search', files=files, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API Response received successfully")
            print(f"   - Status Code: {response.status_code}")
            print(f"   - Success: {data.get('success', False)}")
            
            # Step 2: Check if similar_products key exists (frontend expects this)
            if 'similar_products' in data:
                products = data['similar_products']
                print(f"   - Similar Products Found: {len(products)}")
                
                if products:
                    print("   - First Product Structure:")
                    first_product = products[0]
                    required_fields = ['id', 'name', 'price', 'image', 'similarity']
                    
                    for field in required_fields:
                        if field in first_product:
                            print(f"     ✅ {field}: {first_product[field]}")
                        else:
                            print(f"     ❌ Missing {field}")
                    
                    # Step 3: Test frontend data processing logic
                    print("\nStep 3: Testing frontend data processing...")
                    
                    # Simulate frontend getSortedAndFilteredResults logic
                    products_array = data.get('similar_products', [])
                    if products_array and len(products_array) > 0:
                        print(f"✅ Frontend would receive {len(products_array)} products")
                        
                        # Test similarity score handling
                        for i, product in enumerate(products_array[:3]):
                            similarity_score = product.get('similarity', 0)
                            similarity_percentage = round(similarity_score * 100)
                            print(f"   - Product {i+1}: {product.get('name', 'Unknown')} - {similarity_percentage}% match")
                        
                        return True
                    else:
                        print("❌ Frontend would receive empty products array")
                        return False
                else:
                    print("❌ No products in similar_products array")
                    return False
            else:
                print("❌ Missing 'similar_products' key in response")
                print("   Available keys:", list(data.keys()))
                return False
                
        else:
            print(f"❌ API returned status code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_visual_search_results_page_logic():
    """Test the logic that VisualSearchResults page uses"""
    print("\n📄 TESTING VISUAL SEARCH RESULTS PAGE LOGIC")
    print("=" * 60)
    
    try:
        # Get sample data
        test_image = create_test_image()
        files = {'image': ('test.jpg', test_image, 'image/jpeg')}
        response = requests.post('http://localhost:5000/api/visual_search', files=files, timeout=30)
        
        if response.status_code == 200:
            search_results = response.json()
            
            # Simulate the getSortedAndFilteredResults function
            print("Simulating getSortedAndFilteredResults function...")
            
            # Handle both direct API response and production API response
            products_array = search_results.get('similar_products') or search_results.get('results') or []
            print(f"   - Products array length: {len(products_array)}")
            
            if not products_array or len(products_array) == 0:
                print("❌ Frontend would show 'No products match your filters'")
                return False
            
            products = list(products_array)  # Copy array
            
            # Test filtering logic
            print("   - Testing filter logic...")
            
            # Filter by high similarity (>= 0.8)
            high_similarity_products = [p for p in products if (p.get('similarity', 0) >= 0.8)]
            print(f"     * High similarity products: {len(high_similarity_products)}")
            
            # Filter by in stock
            in_stock_products = [p for p in products if (p.get('stockQuantity', 0) > 0)]
            print(f"     * In stock products: {len(in_stock_products)}")
            
            # Test sorting logic
            print("   - Testing sort logic...")
            
            # Sort by similarity
            products_by_similarity = sorted(products, key=lambda x: x.get('similarity', 0), reverse=True)
            if products_by_similarity:
                best_match = products_by_similarity[0]
                print(f"     * Best match: {best_match.get('name', 'Unknown')} ({round(best_match.get('similarity', 0) * 100)}%)")
            
            print("✅ Frontend logic would work correctly")
            return True
            
        else:
            print(f"❌ Could not get test data: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Run frontend visual search integration tests"""
    print("🚀 FRONTEND VISUAL SEARCH INTEGRATION TESTING")
    print("=" * 80)
    
    # Test results
    results = {
        'api_flow': False,
        'page_logic': False
    }
    
    # Run tests
    results['api_flow'] = test_frontend_visual_search_flow()
    results['page_logic'] = test_visual_search_results_page_logic()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 FRONTEND VISUAL SEARCH INTEGRATION TEST SUMMARY")
    print("=" * 80)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} - {test_name.replace('_', ' ').title()}")
    
    print(f"\n🎯 Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 FRONTEND VISUAL SEARCH INTEGRATION IS WORKING!")
        print("✅ API returns correct data structure")
        print("✅ Frontend logic processes data correctly")
        print("✅ Visual search results should display properly")
    else:
        print("❌ FRONTEND VISUAL SEARCH INTEGRATION ISSUES DETECTED")
        print("🚨 Visual search results may not display correctly")
    
    print("=" * 80)
    return passed_tests == total_tests

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
