#!/usr/bin/env python3
"""
Fix Final Index Optimizations Script
====================================

This script fixes the remaining 19 index optimization issues by adding
performance indexes on the newly created created_at columns.

These indexes are important for:
- Fast date-based queries and filtering
- Performance optimization for reporting
- Efficient data retrieval for analytics
- Production-grade query performance

Usage:
    python fix_final_index_optimizations.py [--backup]
"""

import os
import sys
import argparse
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

class IndexOptimizer:
    def __init__(self):
        self.indexes_added = []
        self.indexes_failed = []
        
    def log_success(self, table, column, sql):
        """Log a successful index creation"""
        self.indexes_added.append({
            'table': table,
            'column': column,
            'sql': sql,
            'timestamp': datetime.now()
        })
    
    def log_failure(self, table, column, error):
        """Log a failed index creation"""
        self.indexes_failed.append({
            'table': table,
            'column': column,
            'error': str(error),
            'timestamp': datetime.now()
        })

    def add_created_at_indexes(self, connection):
        """Add indexes on created_at columns for performance optimization"""
        print("🔧 ADDING CREATED_AT COLUMN INDEXES")
        print("=" * 45)
        
        # Tables that got created_at columns and need indexes
        tables_needing_indexes = [
            'chat_session', 'cookie_consent', 'coupon_usage', 'inventory_sync_log',
            'order_item', 'payment_transaction', 'price_history', 'product_variant',
            'recently_viewed', 'refund', 'rma_approval', 'rma_document', 'sales',
            'search_clicks', 'search_conversions', 'user_interaction_logs',
            'user_sessions', 'visual_search_analytics', 'wishlist'
        ]
        
        indexes_added = 0
        indexes_failed = 0
        
        for table_name in tables_needing_indexes:
            try:
                # Check if table exists
                result = connection.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                if not result.fetchone():
                    print(f"  ⚠️  {table_name}: Table not found, skipping")
                    continue
                
                # Check if created_at column exists
                result = connection.execute(text(f"SHOW COLUMNS FROM {table_name} LIKE 'created_at'"))
                if not result.fetchone():
                    print(f"  ⚠️  {table_name}: created_at column not found, skipping")
                    continue
                
                # Check if index already exists
                index_name = f"idx_{table_name}_created_at"
                result = connection.execute(text(f"SHOW INDEX FROM {table_name} WHERE Key_name = '{index_name}'"))
                if result.fetchone():
                    print(f"  ✅ {table_name}: created_at index already exists")
                    continue
                
                # Create index on created_at column
                index_sql = f"CREATE INDEX {index_name} ON {table_name}(created_at)"
                
                connection.execute(text(index_sql))
                connection.commit()
                
                self.log_success(table_name, 'created_at', index_sql)
                print(f"  ✅ {table_name}: Added created_at index")
                indexes_added += 1
                
            except Exception as e:
                self.log_failure(table_name, 'created_at', str(e))
                print(f"  ❌ {table_name}: Failed - {str(e)}")
                indexes_failed += 1
        
        print(f"\n📊 CREATED_AT INDEX SUMMARY:")
        print(f"  ✅ Successful: {indexes_added}")
        print(f"  ❌ Failed: {indexes_failed}")
        
        return indexes_failed == 0

    def add_additional_performance_indexes(self, connection):
        """Add additional performance indexes for commonly queried columns"""
        print("\n🚀 ADDING ADDITIONAL PERFORMANCE INDEXES")
        print("=" * 50)
        
        # Additional indexes for performance optimization
        additional_indexes = [
            # Status columns for filtering
            ('chat_session', 'status', 'idx_chat_session_status'),
            ('cookie_consent', 'consent_given', 'idx_cookie_consent_given'),
            ('inventory_sync_log', 'sync_status', 'idx_inventory_sync_status'),
            ('payment_transaction', 'transaction_status', 'idx_payment_transaction_status'),
            ('refund', 'refund_status', 'idx_refund_status'),
            ('rma_approval', 'approval_status', 'idx_rma_approval_status'),
            ('user_sessions', 'is_active', 'idx_user_sessions_active'),
            ('visual_search_analytics', 'search_type', 'idx_visual_search_type'),
            
            # Foreign key columns for joins
            ('order_item', 'order_id', 'idx_order_item_order_id'),
            ('order_item', 'product_id', 'idx_order_item_product_id'),
            ('coupon_usage', 'coupon_id', 'idx_coupon_usage_coupon_id'),
            ('coupon_usage', 'user_id', 'idx_coupon_usage_user_id'),
            ('price_history', 'product_id', 'idx_price_history_product_id'),
            ('product_variant', 'product_id', 'idx_product_variant_product_id'),
            ('recently_viewed', 'user_id', 'idx_recently_viewed_user_id'),
            ('recently_viewed', 'product_id', 'idx_recently_viewed_product_id'),
            ('search_clicks', 'search_analytics_id', 'idx_search_clicks_analytics_id'),
            ('search_conversions', 'search_analytics_id', 'idx_search_conversions_analytics_id'),
            ('user_interaction_logs', 'user_id', 'idx_user_interaction_user_id'),
            ('wishlist', 'user_id', 'idx_wishlist_user_id'),
            ('wishlist', 'product_id', 'idx_wishlist_product_id'),
        ]
        
        indexes_added = 0
        indexes_skipped = 0
        
        for table_name, column_name, index_name in additional_indexes:
            try:
                # Check if table exists
                result = connection.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                if not result.fetchone():
                    indexes_skipped += 1
                    continue
                
                # Check if column exists
                result = connection.execute(text(f"SHOW COLUMNS FROM {table_name} LIKE '{column_name}'"))
                if not result.fetchone():
                    indexes_skipped += 1
                    continue
                
                # Check if index already exists
                result = connection.execute(text(f"SHOW INDEX FROM {table_name} WHERE Key_name = '{index_name}'"))
                if result.fetchone():
                    indexes_skipped += 1
                    continue
                
                # Create index
                index_sql = f"CREATE INDEX {index_name} ON {table_name}({column_name})"
                
                connection.execute(text(index_sql))
                connection.commit()
                
                self.log_success(table_name, column_name, index_sql)
                print(f"  ✅ {table_name}.{column_name}: Added performance index")
                indexes_added += 1
                
            except Exception as e:
                # Don't count as failure since these are optional optimizations
                print(f"  ⚠️  {table_name}.{column_name}: Could not add index - {str(e)}")
                indexes_skipped += 1
        
        print(f"\n📊 ADDITIONAL INDEX SUMMARY:")
        print(f"  ✅ Added: {indexes_added}")
        print(f"  ⚠️  Skipped: {indexes_skipped}")
        
        return True

    def create_backup(self, connection):
        """Create a backup before making changes"""
        print("💾 CREATING BACKUP")
        print("=" * 25)
        
        try:
            backup_filename = f"database_backup_before_final_indexes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            
            # Get list of all tables
            result = connection.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            
            backup_content = [
                f"-- Database Backup Before Final Index Optimizations",
                f"-- Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"-- Tables: {len(tables)}",
                f"",
                f"USE allora_db;",
                f""
            ]
            
            # Export table structures
            for table_name in tables:
                try:
                    result = connection.execute(text(f"SHOW CREATE TABLE `{table_name}`"))
                    table_create = result.fetchone()
                    
                    backup_content.extend([
                        f"-- Table: {table_name}",
                        str(table_create[1]) + ";",
                        ""
                    ])
                except Exception as e:
                    backup_content.append(f"-- ERROR: Could not backup table {table_name}: {str(e)}")
            
            # Write backup file
            with open(backup_filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(backup_content))
            
            print(f"✅ Backup created: {backup_filename}")
            return backup_filename
            
        except Exception as e:
            print(f"❌ Backup failed: {str(e)}")
            return None

    def verify_indexes(self, connection):
        """Verify that indexes were created correctly"""
        print("\n✅ VERIFYING INDEX CREATION")
        print("=" * 35)
        
        verification_passed = 0
        verification_failed = 0
        
        # Verify created_at indexes
        tables_with_created_at = [
            'chat_session', 'cookie_consent', 'coupon_usage', 'inventory_sync_log',
            'order_item', 'payment_transaction', 'price_history', 'product_variant',
            'recently_viewed', 'refund', 'rma_approval', 'rma_document', 'sales',
            'search_clicks', 'search_conversions', 'user_interaction_logs',
            'user_sessions', 'visual_search_analytics', 'wishlist'
        ]
        
        for table_name in tables_with_created_at:
            try:
                result = connection.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                if not result.fetchone():
                    continue
                    
                index_name = f"idx_{table_name}_created_at"
                result = connection.execute(text(f"SHOW INDEX FROM {table_name} WHERE Key_name = '{index_name}'"))
                if result.fetchone():
                    verification_passed += 1
                else:
                    verification_failed += 1
                    print(f"  ❌ {table_name}: created_at index missing")
                    
            except Exception as e:
                verification_failed += 1
                print(f"  ❌ {table_name}: Verification error - {str(e)}")
        
        print(f"\n📊 INDEX VERIFICATION RESULTS:")
        print(f"  ✅ Verified: {verification_passed}")
        print(f"  ❌ Missing: {verification_failed}")
        
        # Show total index count
        try:
            result = connection.execute(text("""
                SELECT COUNT(*) as total_indexes
                FROM information_schema.statistics 
                WHERE table_schema = 'allora_db'
                AND index_name != 'PRIMARY'
            """))
            total_indexes = result.fetchone()[0]
            print(f"  📊 Total Indexes: {total_indexes}")
            
        except Exception as e:
            print(f"  ⚠️  Could not count total indexes: {str(e)}")
        
        return verification_failed == 0

    def generate_summary_report(self):
        """Generate a summary report of all index optimizations"""
        print(f"\n📋 FINAL INDEX OPTIMIZATION REPORT")
        print("=" * 55)
        
        if self.indexes_added:
            print(f"\n✅ INDEXES SUCCESSFULLY ADDED ({len(self.indexes_added)}):")
            for idx in self.indexes_added:
                print(f"  • {idx['table']}.{idx['column']}")
                print(f"    Time: {idx['timestamp'].strftime('%H:%M:%S')}")
        
        if self.indexes_failed:
            print(f"\n❌ INDEXES THAT FAILED ({len(self.indexes_failed)}):")
            for idx in self.indexes_failed:
                print(f"  • {idx['table']}.{idx['column']}")
                print(f"    Error: {idx['error']}")
                print(f"    Time: {idx['timestamp'].strftime('%H:%M:%S')}")
        
        total_attempted = len(self.indexes_added) + len(self.indexes_failed)
        success_rate = (len(self.indexes_added) / total_attempted * 100) if total_attempted > 0 else 100
        
        print(f"\n📊 OPTIMIZATION STATISTICS:")
        print(f"  Total Indexes Attempted: {total_attempted}")
        print(f"  Successful Indexes: {len(self.indexes_added)}")
        print(f"  Failed Indexes: {len(self.indexes_failed)}")
        print(f"  Success Rate: {success_rate:.1f}%")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Fix final index optimization issues')
    parser.add_argument('--backup', action='store_true', help='Create backup before optimizations')
    
    args = parser.parse_args()
    
    print("🚀 FINAL INDEX OPTIMIZATION")
    print("=" * 50)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    optimizer = IndexOptimizer()
    success = True
    
    with app.app_context():
        try:
            with db.engine.connect() as connection:
                # Step 1: Create backup if requested
                if args.backup:
                    backup_file = optimizer.create_backup(connection)
                    if not backup_file:
                        print("❌ Backup failed, aborting for safety")
                        return False
                
                # Step 2: Add created_at indexes
                if not optimizer.add_created_at_indexes(connection):
                    success = False
                
                # Step 3: Add additional performance indexes
                optimizer.add_additional_performance_indexes(connection)
                
                # Step 4: Verify indexes
                if not optimizer.verify_indexes(connection):
                    success = False
                
                # Step 5: Generate report
                optimizer.generate_summary_report()
                
        except Exception as e:
            print(f"❌ Index optimization failed: {e}")
            success = False
    
    if success:
        print(f"\n🎉 SUCCESS: All index optimizations completed!")
        print("✅ Database performance is now maximized")
        print("\n📋 NEXT STEPS:")
        print("1. Run comprehensive database test to verify perfect score")
        print("2. Database should now have 0 remaining issues")
        print("3. Performance is optimized for production workloads")
    else:
        print(f"\n⚠️  PARTIAL SUCCESS: Some optimizations may have failed")
        print("Check the detailed report above for specifics")
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
