#!/usr/bin/env python3
"""
Test Script for Allora Logging System
=====================================

This script tests the comprehensive logging functionality including:
- File and console logging
- Log rotation
- Performance logging
- Error logging
- JSON formatting

Usage:
    python test_logging.py

Author: Allora Development Team
Date: 2025-07-08
"""

import os
import sys
import time
import json
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set environment variables for testing
os.environ['LOG_LEVEL'] = 'DEBUG'
os.environ['LOG_FILE'] = 'logs/test_allora.log'
os.environ['LOG_FORMAT'] = 'text'  # Change to 'json' to test JSON logging
os.environ['ENABLE_PERFORMANCE_LOGGING'] = 'true'

from logging_config import setup_application_logging, get_logger, allora_logger, log_performance, log_api_call


def test_basic_logging():
    """Test basic logging functionality"""
    print("🧪 Testing Basic Logging...")
    
    logger = get_logger('test_basic')
    
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    logger.critical("This is a critical message")
    
    print("✅ Basic logging test completed")


def test_performance_logging():
    """Test performance logging"""
    print("🧪 Testing Performance Logging...")
    
    # Test performance logging directly
    allora_logger.log_performance("test_operation", 0.123, user_id="test_user")
    
    # Test performance decorator
    @log_performance("decorated_function")
    def slow_function():
        time.sleep(0.1)  # Simulate slow operation
        return "completed"
    
    result = slow_function()
    print(f"✅ Performance logging test completed: {result}")


def test_user_action_logging():
    """Test user action logging"""
    print("🧪 Testing User Action Logging...")
    
    allora_logger.log_user_action(
        user_id="user123",
        action="product_purchase",
        details={
            "product_id": "prod456",
            "amount": 99.99,
            "payment_method": "credit_card"
        }
    )
    
    print("✅ User action logging test completed")


def test_database_logging():
    """Test database operation logging"""
    print("🧪 Testing Database Logging...")
    
    allora_logger.log_database_operation("SELECT", "products", duration=0.045)
    allora_logger.log_database_operation("INSERT", "orders", duration=0.123)
    allora_logger.log_database_operation("UPDATE", "users")
    
    print("✅ Database logging test completed")


def test_api_logging():
    """Test API request logging"""
    print("🧪 Testing API Logging...")
    
    allora_logger.log_api_request(
        method="GET",
        endpoint="/api/products",
        user_id="user123",
        ip_address="*************",
        duration=0.234
    )
    
    allora_logger.log_api_request(
        method="POST",
        endpoint="/api/orders",
        user_id="user456",
        ip_address="*************",
        duration=0.567
    )
    
    print("✅ API logging test completed")


def test_error_logging():
    """Test error logging with exception info"""
    print("🧪 Testing Error Logging...")
    
    logger = get_logger('test_error')
    
    try:
        # Simulate an error
        result = 1 / 0
    except Exception as e:
        logger.error("Division by zero error occurred", exc_info=True)
    
    # Test error with context
    try:
        data = json.loads("invalid json")
    except Exception as e:
        logger.error(
            "JSON parsing failed",
            extra={
                'user_id': 'user789',
                'input_data': 'invalid json',
                'operation': 'parse_user_preferences'
            },
            exc_info=True
        )
    
    print("✅ Error logging test completed")


def test_log_files_created():
    """Test that log files are created properly"""
    print("🧪 Testing Log File Creation...")
    
    log_file = os.getenv('LOG_FILE', 'logs/test_allora.log')
    log_dir = os.path.dirname(log_file)
    
    # Check if log directory exists
    if os.path.exists(log_dir):
        print(f"✅ Log directory exists: {log_dir}")
    else:
        print(f"❌ Log directory missing: {log_dir}")
        return False
    
    # Check if main log file exists
    if os.path.exists(log_file):
        print(f"✅ Main log file exists: {log_file}")
        
        # Check file size
        size = os.path.getsize(log_file)
        print(f"📊 Log file size: {size} bytes")
    else:
        print(f"❌ Main log file missing: {log_file}")
        return False
    
    # Check for error log file
    error_file = log_file.replace('.log', '_errors.log')
    if os.path.exists(error_file):
        print(f"✅ Error log file exists: {error_file}")
    else:
        print(f"⚠️ Error log file not created yet: {error_file}")
    
    # Check for performance log file
    perf_file = log_file.replace('.log', '_performance.log')
    if os.path.exists(perf_file):
        print(f"✅ Performance log file exists: {perf_file}")
    else:
        print(f"⚠️ Performance log file not created yet: {perf_file}")
    
    return True


def main():
    """Run all logging tests"""
    print("🚀 Starting Allora Logging System Tests")
    print("=" * 50)
    
    # Initialize logging system
    logger = setup_application_logging()
    
    # Run tests
    test_basic_logging()
    test_performance_logging()
    test_user_action_logging()
    test_database_logging()
    test_api_logging()
    test_error_logging()
    
    # Check file creation
    test_log_files_created()
    
    print("=" * 50)
    print("✅ All logging tests completed!")
    print(f"📝 Check log files in: {os.path.dirname(os.getenv('LOG_FILE', 'logs/test_allora.log'))}")
    
    # Display recent log entries
    log_file = os.getenv('LOG_FILE', 'logs/test_allora.log')
    if os.path.exists(log_file):
        print("\n📋 Recent log entries:")
        print("-" * 30)
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[-10:]:  # Show last 10 lines
                print(line.strip())


if __name__ == "__main__":
    main()
