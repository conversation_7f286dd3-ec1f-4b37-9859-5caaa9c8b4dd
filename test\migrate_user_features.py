#!/usr/bin/env python3
"""
Database migration script to add user account features.
This script adds new columns to the User table and creates new tables for
addresses, payment methods, wishlist, orders, and order items.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

def run_migration():
    """Run the database migration to add user account features."""
    
    with app.app_context():
        try:
            print("Starting database migration for user account features...")
            
            # Add new columns to User table
            print("Adding new columns to User table...")
            
            # Check if columns already exist before adding them
            user_columns_to_add = [
                "ALTER TABLE user ADD COLUMN first_name VA<PERSON>HA<PERSON>(50)",
                "ALTER TABLE user ADD COLUMN last_name VA<PERSON><PERSON><PERSON>(50)",
                "ALTER TABLE user ADD COLUMN phone VARCHAR(20)",
                "ALTER TABLE user ADD COLUMN date_of_birth DATE",
                "ALTER TABLE user ADD COLUMN profile_picture VARCHAR(255)",
                "ALTER TABLE user ADD COLUMN bio TEXT",
                "ALTER TABLE user ADD COLUMN newsletter_subscribed BOOLEAN DEFAULT 1",
                "ALTER TABLE user ADD COLUMN email_notifications BOOLEAN DEFAULT 1",
                "ALTER TABLE user ADD COLUMN sms_notifications BOOLEAN DEFAULT 0",
                "ALTER TABLE user ADD COLUMN preferred_language VARCHAR(10) DEFAULT 'en'",
                "ALTER TABLE user ADD COLUMN preferred_currency VARCHAR(3) DEFAULT 'INR'",
                "ALTER TABLE user ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                "ALTER TABLE user ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                "ALTER TABLE user ADD COLUMN last_login DATETIME",
                "ALTER TABLE user ADD COLUMN is_active BOOLEAN DEFAULT 1"
            ]
            
            for sql in user_columns_to_add:
                try:
                    db.session.execute(text(sql))
                    print(f"✓ Executed: {sql}")
                except Exception as e:
                    if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                        print(f"⚠ Column already exists: {sql}")
                    else:
                        print(f"✗ Error executing {sql}: {e}")
            
            # Create new tables
            print("\nCreating new tables...")
            db.create_all()
            print("✓ All tables created successfully")
            
            # Commit all changes
            db.session.commit()
            print("\n✅ Migration completed successfully!")
            
            # Print summary
            print("\n📋 Migration Summary:")
            print("- Extended User model with profile fields and preferences")
            print("- Created UserAddress table for address management")
            print("- Created PaymentMethod table for payment method storage")
            print("- Created Wishlist table for save-for-later functionality")
            print("- Created Order and OrderItem tables for order history")
            print("- All API endpoints have been added to app.py")
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            db.session.rollback()
            return False
            
    return True

def verify_migration():
    """Verify that the migration was successful."""

    with app.app_context():
        try:
            print("\n🔍 Verifying migration...")

            # Check if new tables exist (MySQL version)
            tables_to_check = ['user_address', 'payment_method', 'wishlist', 'order', 'order_item']

            for table_name in tables_to_check:
                result = db.session.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                if result.fetchone():
                    print(f"✓ Table '{table_name}' exists")
                else:
                    print(f"✗ Table '{table_name}' missing")
                    return False

            # Check if new User columns exist (MySQL version)
            result = db.session.execute(text("DESCRIBE user"))
            columns = [row[0] for row in result.fetchall()]

            required_columns = ['first_name', 'last_name', 'phone', 'profile_picture', 'bio',
                              'newsletter_subscribed', 'email_notifications', 'preferred_language']

            for col in required_columns:
                if col in columns:
                    print(f"✓ User column '{col}' exists")
                else:
                    print(f"✗ User column '{col}' missing")
                    return False

            print("✅ Migration verification successful!")
            return True

        except Exception as e:
            print(f"❌ Migration verification failed: {e}")
            return False

if __name__ == "__main__":
    print("🚀 User Account Features Migration")
    print("=" * 50)
    
    if run_migration():
        if verify_migration():
            print("\n🎉 Migration completed and verified successfully!")
            print("\nNext steps:")
            print("1. Test the new API endpoints")
            print("2. Create frontend components for user account features")
            print("3. Update existing components to use new profile fields")
        else:
            print("\n⚠ Migration completed but verification failed")
    else:
        print("\n❌ Migration failed")
        sys.exit(1)
