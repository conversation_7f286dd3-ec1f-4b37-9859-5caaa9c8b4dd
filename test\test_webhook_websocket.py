#!/usr/bin/env python3
"""
Webhook and WebSocket Testing Script
===================================

This script tests the webhook and websocket functionality in the Allora backend
to identify configuration issues and verify proper operation.

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import sys
import json
import time
import requests
import threading
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_webhook_integration():
    """Test webhook integration and configuration"""
    print("🔍 Testing Webhook Integration...")
    
    try:
        from webhook_handlers import webhook_bp, WEBHOOK_CONFIGS, WebhookCarrier, WebhookSecurity
        print("✅ Webhook handlers imported successfully")
        
        # Test webhook configurations
        print(f"✅ Webhook configurations loaded: {len(WEBHOOK_CONFIGS)} carriers")
        for carrier, config in WEBHOOK_CONFIGS.items():
            print(f"   - {carrier.value}: {config.signature_method}")
        
        # Test security functions
        test_payload = b'{"test": "data"}'
        test_secret = "test_secret"
        test_signature = WebhookSecurity.verify_signature(
            test_payload, "invalid_signature", test_secret, "hmac_sha256"
        )
        print(f"✅ Webhook security verification working: {not test_signature}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Webhook import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Webhook test failed: {e}")
        return False

def test_websocket_integration():
    """Test websocket integration and configuration"""
    print("\n🔍 Testing WebSocket Integration...")
    
    try:
        # Test websocket_manager import
        from websocket_manager import manager, ConnectionManager, WebSocketEventHandler
        print("✅ WebSocket manager imported successfully")
        
        # Test connection manager
        print(f"✅ Connection manager initialized")
        print(f"   - Active connections: {len(manager.active_connections)}")
        print(f"   - Guest connections: {len(manager.guest_connections)}")
        print(f"   - All connections: {len(manager.all_connections)}")
        
        # Test Redis connection
        try:
            manager.redis_client.ping()
            print("✅ Redis connection for WebSocket working")
        except Exception as e:
            print(f"⚠️  Redis connection issue: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ WebSocket import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False

def test_fastapi_dependencies():
    """Test FastAPI dependencies for WebSocket routes"""
    print("\n🔍 Testing FastAPI Dependencies...")
    
    try:
        import fastapi
        print(f"✅ FastAPI version: {fastapi.__version__}")
        
        from websocket_routes import router
        print("✅ WebSocket routes imported successfully")
        
        # Check if routes are properly defined
        routes = router.routes
        print(f"✅ WebSocket routes defined: {len(routes)}")
        
        for route in routes:
            if hasattr(route, 'path'):
                print(f"   - {route.path}")
        
        return True
        
    except ImportError as e:
        print(f"❌ FastAPI dependencies missing: {e}")
        print("💡 Install FastAPI: pip install fastapi uvicorn")
        return False
    except Exception as e:
        print(f"❌ FastAPI test failed: {e}")
        return False

def test_missing_dependencies():
    """Test for missing dependencies"""
    print("\n🔍 Testing Missing Dependencies...")
    
    missing_deps = []
    
    # Check FastAPI
    try:
        import fastapi
        print("✅ FastAPI available")
    except ImportError:
        missing_deps.append("fastapi")
        print("❌ FastAPI missing")
    
    # Check uvicorn
    try:
        import uvicorn
        print("✅ Uvicorn available")
    except ImportError:
        missing_deps.append("uvicorn")
        print("❌ Uvicorn missing")
    
    # Check websockets
    try:
        import websockets
        print("✅ WebSockets library available")
    except ImportError:
        missing_deps.append("websockets")
        print("❌ WebSockets library missing")
    
    if missing_deps:
        print(f"\n💡 Install missing dependencies:")
        print(f"   pip install {' '.join(missing_deps)}")
        return False
    
    return True

def test_app_integration():
    """Test integration with main Flask app"""
    print("\n🔍 Testing App Integration...")

    try:
        # Test if app can be imported without starting it
        import app
        print("✅ App module imported successfully")

        # Check if webhook blueprint exists
        try:
            from webhook_handlers import webhook_bp
            print("✅ Webhook blueprint available")
        except ImportError:
            print("❌ Webhook blueprint not available")
            return False

        return True

    except Exception as e:
        print(f"❌ App integration test failed: {e}")
        return False

def test_webhook_endpoints():
    """Test webhook endpoints if server is running"""
    print("\n🔍 Testing Webhook Endpoints...")

    print("⚠️  Skipping endpoint tests (requires running server)")
    print("   To test endpoints manually:")
    print("   1. Start server: python run_with_waitress.py")
    print("   2. Test: curl -X POST http://localhost:5000/api/webhooks/test -H 'Content-Type: application/json' -d '{\"test\":\"data\"}'")

    return True

def test_configuration_issues():
    """Test for configuration issues"""
    print("\n🔍 Testing Configuration Issues...")
    
    issues = []
    
    try:
        # Check webhook configurations
        from webhook_handlers import WEBHOOK_CONFIGS
        
        for carrier, config in WEBHOOK_CONFIGS.items():
            if config.secret_key.startswith("your_"):
                issues.append(f"Webhook secret not configured for {carrier.value}")
        
        # Check websocket configuration
        from websocket_manager import manager
        
        try:
            manager.redis_client.ping()
        except:
            issues.append("Redis not available for WebSocket pub/sub")
        
        if issues:
            print("⚠️  Configuration Issues Found:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ No configuration issues found")
            return True
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def generate_fixes():
    """Generate fixes for identified issues"""
    print("\n🔧 Generating Fixes...")
    
    fixes = []
    
    # Check if FastAPI is missing
    try:
        import fastapi
    except ImportError:
        fixes.append({
            "issue": "FastAPI not installed",
            "fix": "pip install fastapi uvicorn websockets",
            "description": "Install FastAPI and dependencies for WebSocket support"
        })
    
    # Check webhook configuration
    try:
        from webhook_handlers import WEBHOOK_CONFIGS
        for carrier, config in WEBHOOK_CONFIGS.items():
            if config.secret_key.startswith("your_"):
                fixes.append({
                    "issue": f"Webhook secret not configured for {carrier.value}",
                    "fix": f"Set environment variable or update config for {carrier.value}",
                    "description": "Configure proper webhook secrets for security"
                })
    except:
        pass
    
    # Check if WebSocket routes are integrated
    try:
        from app import app
        websocket_routes = [str(rule) for rule in app.url_map.iter_rules() if 'websocket' in str(rule)]
        if not websocket_routes:
            fixes.append({
                "issue": "WebSocket routes not integrated with Flask app",
                "fix": "Add FastAPI integration or create Flask-SocketIO implementation",
                "description": "WebSocket routes are defined but not integrated with main Flask app"
            })
    except:
        pass
    
    return fixes

def main():
    """Main testing function"""
    print("🚀 Webhook and WebSocket Testing Suite")
    print("=" * 60)
    
    results = {}
    
    # Run all tests
    results['webhook_integration'] = test_webhook_integration()
    results['websocket_integration'] = test_websocket_integration()
    results['fastapi_dependencies'] = test_fastapi_dependencies()
    results['missing_dependencies'] = test_missing_dependencies()
    results['app_integration'] = test_app_integration()
    results['webhook_endpoints'] = test_webhook_endpoints()
    results['configuration'] = test_configuration_issues()
    
    # Generate fixes
    fixes = generate_fixes()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if fixes:
        print(f"\n🔧 FIXES NEEDED ({len(fixes)}):")
        print("=" * 40)
        for i, fix in enumerate(fixes, 1):
            print(f"{i}. {fix['issue']}")
            print(f"   Fix: {fix['fix']}")
            print(f"   Description: {fix['description']}")
            print()
    
    if passed == total:
        print("\n🎉 All webhook and websocket features are working perfectly!")
    elif passed >= total * 0.7:
        print("\n✅ Most features working - minor fixes needed")
    else:
        print("\n⚠️  Several issues need attention for proper functionality")
    
    return results, fixes

if __name__ == '__main__':
    main()
