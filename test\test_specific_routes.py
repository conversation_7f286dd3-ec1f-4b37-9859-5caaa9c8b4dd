#!/usr/bin/env python3
"""
Test specific routes that are failing
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_route(method, endpoint, **kwargs):
    """Test a specific route"""
    try:
        if method.upper() == 'GET':
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=10, **kwargs)
        elif method.upper() == 'POST':
            response = requests.post(f"{BASE_URL}{endpoint}", timeout=10, **kwargs)
        
        print(f"{method} {endpoint}: Status {response.status_code}")
        if response.status_code != 200:
            try:
                error_data = response.json()
                print(f"  Error: {error_data}")
            except:
                print(f"  Error: {response.text[:200]}")
        else:
            try:
                data = response.json()
                if isinstance(data, list):
                    print(f"  Success: Got {len(data)} items")
                elif isinstance(data, dict):
                    print(f"  Success: Got dict with keys: {list(data.keys())}")
                else:
                    print(f"  Success: {type(data)}")
            except:
                print(f"  Success: {response.text[:100]}")
        
        return response.status_code
        
    except Exception as e:
        print(f"{method} {endpoint}: ERROR - {str(e)}")
        return None

def main():
    print("🔍 TESTING SPECIFIC FAILING ROUTES")
    print("=" * 50)
    
    # Test routes that are failing
    routes_to_test = [
        ("GET", "/api/products/1"),
        ("GET", "/api/recommendations/similar/1"),
        ("POST", "/api/visual_search"),
        ("GET", "/api/recommendations/personalized"),
        ("GET", "/api/recommendations"),
        ("GET", "/api/recommendations/trending"),
    ]
    
    for method, endpoint in routes_to_test:
        if method == "POST" and "visual_search" in endpoint:
            # Create a dummy file for visual search
            import io
            from PIL import Image
            
            try:
                img = Image.new('RGB', (100, 100), color='red')
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='PNG')
                img_bytes.seek(0)
                
                files = {'image': ('test.png', img_bytes, 'image/png')}
                test_route(method, endpoint, files=files)
            except ImportError:
                print(f"POST {endpoint}: SKIPPED - PIL not available")
        else:
            test_route(method, endpoint)
        
        print()

if __name__ == "__main__":
    main()
