#!/usr/bin/env python3
"""
Comprehensive Data Cleanup Script for Allora E-commerce Platform
================================================================

This script completely clears all dummy, fake, and mock data from:
1. Database - All tables with existing data
2. Backend - Log files, cache files, temporary files
3. Frontend - Build artifacts, cache, temporary files

Usage:
    python clear_all_data.py [--confirm] [--database-only] [--files-only]

Options:
    --confirm       Skip confirmation prompts (use with caution)
    --database-only Only clear database data
    --files-only    Only clear files, skip database
"""

import os
import sys
import shutil
import argparse
from datetime import datetime
import glob

# Add the current directory to Python path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

class DataCleaner:
    """Main class for cleaning all project data"""
    
    def __init__(self, confirm_all=False, database_only=False, files_only=False):
        self.confirm_all = confirm_all
        self.database_only = database_only
        self.files_only = files_only
        self.cleaned_items = []
        
    def log_action(self, action, details=""):
        """Log cleanup actions"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        message = f"[{timestamp}] {action}"
        if details:
            message += f" - {details}"
        print(message)
        self.cleaned_items.append(message)

    def confirm_action(self, message):
        """Ask for user confirmation unless --confirm flag is used"""
        if self.confirm_all:
            return True
        
        response = input(f"{message} (y/N): ").lower().strip()
        return response.startswith('y')

    def clear_database_data(self):
        """Clear all data from database tables"""
        print("\n🗄️  DATABASE CLEANUP")
        print("=" * 50)
        
        if not self.confirm_action("⚠️  This will DELETE ALL DATA from the database. Continue?"):
            print("❌ Database cleanup cancelled")
            return False
        
        try:
            # Import Flask app and models
            from app import app, db
            from app import (
                # Import all models for cleanup
                User, Product, Order, OrderItem, Seller, AdminUser,
                ProductReview, ProductImage, ProductVariant, PriceHistory,
                UserAddress, PaymentMethod, Wishlist, CartItem, UserInteractionLog, UserBehaviorProfile,
                CommunityPost, PostComment, PostLike, Hashtag, PostHashtag, CommunityStats,
                Coupon, CouponUsage, TaxRate, ShippingZone, ShippingMethod,
                Sales, SearchAnalytics, VisualSearchAnalytics, NewsletterSubscription,
                ShippingCarrier, Shipment, TrackingEvent, FulfillmentRule, CarrierRate,
                RMARequest, RMAItem, RMATimeline, RMADocument, RMAApproval, RMARule, RMAConfiguration, ReturnShipment,
                SupportTicket, SupportMessage, SupportAttachment,
                PaymentGateway, PaymentTransaction, Invoice, Refund,
                SellerStore, SellerCommission, SellerPayout,
                ContentPage, Banner, RecentlyViewed, AvailabilityNotification,
                InventoryLog, SalesChannel, ChannelInventory, InventorySyncLog,
                OAuthProvider, UserOAuth,
                EmailNotification, GuestSession, SavedCart, AbandonedCart,
                CookieConsent, DataExportRequest
            )
            
            with app.app_context():
                self.log_action("🔄 Starting database cleanup...")
                
                # Order matters due to foreign key constraints - clear dependent tables first
                tables_to_clear = [
                    # Dependent tables first
                    (OrderItem, "Order Items"),
                    (CartItem, "Cart Items"),
                    (Wishlist, "Wishlist Items"),
                    (ProductReview, "Product Reviews"),
                    (ProductImage, "Product Images"),
                    (ProductVariant, "Product Variants"),
                    (RecentlyViewed, "Recently Viewed"),
                    (AvailabilityNotification, "Availability Notifications"),
                    (Sales, "Sales Records"),
                    (PriceHistory, "Price History"),
                    (PostComment, "Post Comments"),
                    (PostLike, "Post Likes"),
                    (PostHashtag, "Post Hashtags"),
                    (UserInteractionLog, "User Interaction Logs"),
                    (UserBehaviorProfile, "User Behavior Profiles"),
                    (CouponUsage, "Coupon Usage"),
                    (SellerCommission, "Seller Commissions"),
                    (PaymentTransaction, "Payment Transactions"),
                    (Invoice, "Invoices"),
                    (Refund, "Refunds"),
                    (RMAItem, "RMA Items"),
                    (RMATimeline, "RMA Timeline"),
                    (RMADocument, "RMA Documents"),
                    (RMAApproval, "RMA Approvals"),
                    (ReturnShipment, "Return Shipments"),
                    (TrackingEvent, "Tracking Events"),
                    (Shipment, "Shipments"),
                    (SupportMessage, "Support Messages"),
                    (SupportAttachment, "Support Attachments"),
                    (InventoryLog, "Inventory Logs"),
                    (ChannelInventory, "Channel Inventory"),
                    (InventorySyncLog, "Inventory Sync Logs"),
                    (EmailNotification, "Email Notifications"),
                    (SearchAnalytics, "Search Analytics"),
                    (VisualSearchAnalytics, "Visual Search Analytics"),
                    (UserAddress, "User Addresses"),
                    (PaymentMethod, "Payment Methods"),
                    (UserOAuth, "User OAuth"),
                    
                    # Main tables
                    (Order, "Orders"),
                    (Product, "Products"),
                    (CommunityPost, "Community Posts"),
                    (Hashtag, "Hashtags"),
                    (User, "Users"),
                    (Seller, "Sellers"),
                    (AdminUser, "Admin Users"),
                    (SupportTicket, "Support Tickets"),
                    (RMARequest, "RMA Requests"),
                    (PaymentGateway, "Payment Gateways"),
                    (ShippingCarrier, "Shipping Carriers"),
                    (Coupon, "Coupons"),
                    (TaxRate, "Tax Rates"),
                    (ShippingZone, "Shipping Zones"),
                    (ShippingMethod, "Shipping Methods"),
                    (FulfillmentRule, "Fulfillment Rules"),
                    (CarrierRate, "Carrier Rates"),
                    (RMARule, "RMA Rules"),
                    (RMAConfiguration, "RMA Configuration"),
                    (SalesChannel, "Sales Channels"),
                    (ContentPage, "Content Pages"),
                    (Banner, "Banners"),
                    (OAuthProvider, "OAuth Providers"),
                    (GuestSession, "Guest Sessions"),
                    (SavedCart, "Saved Carts"),
                    (AbandonedCart, "Abandoned Carts"),
                    (NewsletterSubscription, "Newsletter Subscriptions"),
                    (CookieConsent, "Cookie Consents"),
                    (DataExportRequest, "Data Export Requests"),
                    (SellerStore, "Seller Stores"),
                    (SellerPayout, "Seller Payouts"),
                    (CommunityStats, "Community Stats")
                ]
                
                total_cleared = 0
                
                for table_model, table_name in tables_to_clear:
                    try:
                        count = db.session.query(table_model).count()
                        if count > 0:
                            db.session.query(table_model).delete()
                            total_cleared += count
                            self.log_action(f"✅ Cleared {table_name}", f"{count} records")
                        else:
                            self.log_action(f"ℹ️  {table_name} already empty")
                    except Exception as e:
                        self.log_action(f"⚠️  Warning clearing {table_name}", str(e))
                
                # Commit all changes
                db.session.commit()
                self.log_action(f"🎉 Database cleanup completed", f"Total records cleared: {total_cleared}")
                
                return True
                
        except Exception as e:
            self.log_action("❌ Database cleanup failed", str(e))
            return False

    def clear_backend_files(self):
        """Clear backend temporary files, logs, and cache"""
        print("\n🔧 BACKEND FILES CLEANUP")
        print("=" * 50)
        
        backend_path = os.path.dirname(os.path.abspath(__file__))
        
        # Files and directories to clean
        cleanup_patterns = [
            # Log files
            "logs/*.log",
            "logs/*.log.*",
            
            # Python cache
            "__pycache__",
            "*.pyc",
            "*.pyo",
            "*.pyd",
            ".Python",
            
            # ML model files (if any dummy ones exist)
            "models/**/*.pkl",
            "models/**/*.joblib",
            "models/**/*.h5",
            "models/**/*.pb",
            
            # Temporary files
            "*.tmp",
            "*.temp",
            ".DS_Store",
            "Thumbs.db",
            
            # Test files
            "test_*.py",
            "*_test.py",
        ]
        
        cleaned_files = 0
        
        for pattern in cleanup_patterns:
            full_pattern = os.path.join(backend_path, pattern)
            
            if "**" in pattern:
                # Recursive pattern
                matches = glob.glob(full_pattern, recursive=True)
            else:
                matches = glob.glob(full_pattern)
            
            for match in matches:
                try:
                    if os.path.isfile(match):
                        os.remove(match)
                        self.log_action("🗑️  Removed file", os.path.relpath(match, backend_path))
                        cleaned_files += 1
                    elif os.path.isdir(match):
                        shutil.rmtree(match)
                        self.log_action("🗑️  Removed directory", os.path.relpath(match, backend_path))
                        cleaned_files += 1
                except Exception as e:
                    self.log_action("⚠️  Could not remove", f"{match}: {e}")
        
        self.log_action(f"✅ Backend cleanup completed", f"{cleaned_files} items removed")

    def clear_frontend_files(self):
        """Clear frontend build artifacts and cache"""
        print("\n🎨 FRONTEND FILES CLEANUP")
        print("=" * 50)
        
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "frontend")
        
        if not os.path.exists(frontend_path):
            self.log_action("ℹ️  Frontend directory not found", frontend_path)
            return
        
        # Directories and files to clean
        cleanup_items = [
            "build",
            "dist", 
            "node_modules/.cache",
            ".next",
            "coverage",
            "*.log",
            "npm-debug.log*",
            "yarn-debug.log*",
            "yarn-error.log*",
            ".DS_Store",
            "Thumbs.db"
        ]
        
        cleaned_items = 0
        
        for item in cleanup_items:
            item_path = os.path.join(frontend_path, item)
            
            if "*" in item:
                # Pattern matching
                matches = glob.glob(item_path)
                for match in matches:
                    try:
                        if os.path.isfile(match):
                            os.remove(match)
                            self.log_action("🗑️  Removed file", os.path.relpath(match, frontend_path))
                            cleaned_items += 1
                    except Exception as e:
                        self.log_action("⚠️  Could not remove", f"{match}: {e}")
            else:
                try:
                    if os.path.exists(item_path):
                        if os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                            self.log_action("🗑️  Removed directory", item)
                            cleaned_items += 1
                        else:
                            os.remove(item_path)
                            self.log_action("🗑️  Removed file", item)
                            cleaned_items += 1
                except Exception as e:
                    self.log_action("⚠️  Could not remove", f"{item}: {e}")
        
        self.log_action(f"✅ Frontend cleanup completed", f"{cleaned_items} items removed")

    def run_cleanup(self):
        """Run the complete cleanup process"""
        print("🧹 ALLORA PROJECT DATA CLEANUP")
        print("=" * 50)
        print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        if not self.database_only and not self.files_only:
            print("This will clean:")
            print("  📊 Database - All tables and data")
            print("  🔧 Backend - Logs, cache, temporary files")
            print("  🎨 Frontend - Build artifacts, cache")
            print()
        
        success = True
        
        # Database cleanup
        if not self.files_only:
            if not self.clear_database_data():
                success = False
        
        # Backend files cleanup
        if not self.database_only:
            self.clear_backend_files()
        
        # Frontend files cleanup
        if not self.database_only:
            self.clear_frontend_files()
        
        # Summary
        print("\n" + "=" * 50)
        print("📋 CLEANUP SUMMARY")
        print("=" * 50)
        
        for item in self.cleaned_items:
            print(item)
        
        print(f"\n🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if success:
            print("🎉 Cleanup completed successfully!")
            print("\n📋 Next Steps:")
            print("  1. Run the seeding script: python seed_database.py --clear --products=100")
            print("  2. Or use the interactive runner: python run_seeder.py")
        else:
            print("⚠️  Cleanup completed with some warnings. Check the log above.")
        
        return success

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Clear all dummy/fake/mock data from Allora project')
    parser.add_argument('--confirm', action='store_true', help='Skip confirmation prompts')
    parser.add_argument('--database-only', action='store_true', help='Only clear database data')
    parser.add_argument('--files-only', action='store_true', help='Only clear files, skip database')
    
    args = parser.parse_args()
    
    cleaner = DataCleaner(
        confirm_all=args.confirm,
        database_only=args.database_only,
        files_only=args.files_only
    )
    
    cleaner.run_cleanup()

if __name__ == '__main__':
    main()
