#!/usr/bin/env python3
"""
Comprehensive Database Seeding Script for Allora E-commerce Platform
====================================================================

This script seeds the database with realistic, valuable data for all tables:
- 100 products with proper categories, descriptions, and images
- Multiple users, sellers, and admin users
- Orders, reviews, and interactions
- All supporting data for a fully functional e-commerce platform

Usage:
    python seed_database.py [--clear] [--products=100]

Options:
    --clear     Clear existing data before seeding
    --products  Number of products to create (default: 100)
"""

import os
import sys
import random
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
import argparse

# Install faker if not available
try:
    from faker import Faker
except ImportError:
    print("Installing faker...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "faker"])
    from faker import Faker

# Add the current directory to Python path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import Flask app and models
from app import app, db
from app import (
    # Core models
    User, Product, Order, OrderItem, Seller, AdminUser,
    # Product related
    ProductReview, ProductImage, ProductVariant, PriceHistory,
    # User related
    UserAddress, PaymentMethod, Wishlist, CartItem, UserInteractionLog, UserBehaviorProfile,
    # Community
    CommunityPost, PostComment, PostLike, Hashtag, PostHashtag, CommunityStats,
    # Commerce
    Coupon, CouponUsage, TaxRate, ShippingZone, ShippingMethod,
    # Analytics
    Sales, SearchAnalytics, VisualSearchAnalytics, NewsletterSubscription,
    # Fulfillment
    ShippingCarrier, Shipment, TrackingEvent, FulfillmentRule, CarrierRate,
    # RMA
    RMARequest, RMAItem, RMATimeline, RMADocument, RMAApproval, RMARule, RMAConfiguration, ReturnShipment,
    # Support
    SupportTicket, SupportMessage, SupportAttachment,
    # Payment
    PaymentGateway, PaymentTransaction, Invoice, Refund,
    # Seller
    SellerStore, SellerCommission, SellerPayout,
    # Content
    ContentPage, Banner, RecentlyViewed, AvailabilityNotification,
    # Inventory
    InventoryLog, SalesChannel, ChannelInventory, InventorySyncLog,
    # OAuth
    OAuthProvider, UserOAuth,
    # Other
    EmailNotification, GuestSession, SavedCart, AbandonedCart,
    CookieConsent, DataExportRequest
)

# Initialize Faker for generating realistic data
fake = Faker()
Faker.seed(42)  # For reproducible results
random.seed(42)

class DatabaseSeeder:
    """Main seeding class that handles all database seeding operations"""
    
    def __init__(self, num_products=100, clear_data=False):
        self.num_products = num_products
        self.clear_data = clear_data
        self.created_data = {
            'users': [],
            'sellers': [],
            'products': [],
            'orders': [],
            'admin_users': [],
            'categories': [],
            'brands': [],
            'hashtags': [],
            'posts': []
        }
        
        # Sustainable product categories and their subcategories
        self.categories = {
            'Electronics': ['Smartphones', 'Laptops', 'Tablets', 'Headphones', 'Cameras', 'Smart Watches'],
            'Fashion': ['Men\'s Clothing', 'Women\'s Clothing', 'Shoes', 'Accessories', 'Jewelry', 'Bags'],
            'Home & Garden': ['Furniture', 'Kitchen', 'Bedding', 'Decor', 'Garden Tools', 'Lighting'],
            'Sports & Outdoors': ['Fitness Equipment', 'Outdoor Gear', 'Sports Apparel', 'Team Sports', 'Water Sports'],
            'Books & Media': ['Fiction', 'Non-Fiction', 'Educational', 'Comics', 'Movies', 'Music'],
            'Health & Beauty': ['Skincare', 'Makeup', 'Hair Care', 'Supplements', 'Personal Care', 'Fragrances'],
            'Toys & Games': ['Action Figures', 'Board Games', 'Educational Toys', 'Video Games', 'Puzzles'],
            'Automotive': ['Car Parts', 'Tools', 'Accessories', 'Tires', 'Electronics', 'Maintenance'],
            'Food & Beverages': ['Snacks', 'Beverages', 'Organic', 'Gourmet', 'Health Foods', 'International'],
            'Baby & Kids': ['Baby Gear', 'Toys', 'Clothing', 'Feeding', 'Safety', 'Education']
        }

        # Sustainable and eco-friendly brands for different categories
        self.brands = {
            'Electronics': ['Fairphone', 'Framework', 'Apple', 'Samsung', 'Dell', 'HP', 'Logitech', 'Anker'],
            'Fashion': ['Patagonia', 'Everlane', 'Reformation', 'Eileen Fisher', 'Organic Basics', 'Kotn', 'Tentree'],
            'Home & Garden': ['IKEA', 'West Elm', 'Bamboo Studio', 'EcoVibe', 'Green Living', 'Sustainable Home'],
            'Sports & Outdoors': ['Patagonia', 'REI Co-op', 'Allbirds', 'Adidas', 'Nike', 'Outdoor Research', 'Prana'],
            'Health & Beauty': ['The Ordinary', 'Weleda', 'Dr. Bronner\'s', 'Burt\'s Bees', 'Lush', 'Aesop']
        }

        # Sustainable product templates for realistic names
        self.sustainable_products = {
            'Electronics': [
                'Eco-Friendly Wireless Earbuds', 'Solar Power Bank', 'Bamboo Wireless Charger',
                'Refurbished Laptop', 'Energy-Efficient Smart Watch', 'Recycled Plastic Phone Case',
                'Sustainable Bluetooth Speaker', 'Eco Tablet Stand', 'Green Tech Webcam',
                'Solar Charging Station', 'Biodegradable Phone Screen Protector', 'Eco-Friendly Keyboard'
            ],
            'Fashion': [
                'Organic Cotton T-Shirt', 'Recycled Polyester Jacket', 'Hemp Denim Jeans',
                'Bamboo Fiber Socks', 'Eco-Friendly Sneakers', 'Sustainable Wool Sweater',
                'Organic Linen Dress', 'Recycled Cotton Hoodie', 'Eco Leather Handbag',
                'Sustainable Running Shoes', 'Organic Cotton Underwear', 'Hemp Canvas Backpack'
            ],
            'Home & Garden': [
                'Bamboo Kitchen Utensil Set', 'Recycled Glass Dinnerware', 'Organic Cotton Bedding',
                'Solar Garden Lights', 'Compost Bin', 'Eco-Friendly Cleaning Kit',
                'Sustainable Bamboo Cutting Board', 'Organic Cotton Towels', 'Recycled Plastic Planters',
                'Energy-Efficient LED Bulbs', 'Bamboo Storage Containers', 'Eco-Friendly Dish Soap'
            ],
            'Sports & Outdoors': [
                'Eco-Friendly Yoga Mat', 'Sustainable Water Bottle', 'Organic Cotton Workout Clothes',
                'Recycled Plastic Hiking Backpack', 'Bamboo Fiber Activewear', 'Solar Camping Lantern',
                'Eco-Friendly Resistance Bands', 'Sustainable Running Gear', 'Organic Hemp Rope',
                'Recycled Foam Roller', 'Bamboo Bike Accessories', 'Eco-Friendly Sports Towel'
            ],
            'Health & Beauty': [
                'Organic Face Moisturizer', 'Natural Shampoo Bar', 'Eco-Friendly Toothbrush',
                'Sustainable Skincare Set', 'Organic Lip Balm', 'Natural Deodorant',
                'Bamboo Makeup Brushes', 'Eco-Friendly Soap', 'Organic Body Lotion',
                'Natural Face Cleanser', 'Sustainable Hair Oil', 'Eco-Friendly Sunscreen'
            ],
            'Food & Beverages': [
                'Organic Fair Trade Coffee', 'Sustainable Tea Collection', 'Eco-Friendly Protein Bars',
                'Organic Superfood Mix', 'Natural Energy Drinks', 'Sustainable Snack Pack',
                'Organic Nut Butter', 'Fair Trade Chocolate', 'Eco-Friendly Meal Kit',
                'Organic Herbal Tea', 'Sustainable Granola', 'Natural Fruit Juice'
            ],
            'Baby & Kids': [
                'Organic Cotton Baby Clothes', 'Eco-Friendly Wooden Toys', 'Sustainable Baby Bottles',
                'Organic Baby Food', 'Bamboo Baby Utensils', 'Eco-Friendly Diapers',
                'Organic Cotton Blanket', 'Sustainable Teething Toys', 'Natural Baby Lotion',
                'Eco-Friendly Baby Carrier', 'Organic Baby Shampoo', 'Bamboo Baby Plates'
            ],
            'Books & Media': [
                'Sustainable Living Guide', 'Eco-Friendly Cookbook', 'Climate Change Awareness Book',
                'Organic Gardening Manual', 'Zero Waste Lifestyle Book', 'Renewable Energy Guide',
                'Sustainable Fashion Book', 'Green Technology Magazine', 'Environmental Science Textbook',
                'Eco-Friendly DIY Projects', 'Sustainable Business Guide', 'Climate Action Handbook'
            ],
            'Toys & Games': [
                'Wooden Educational Toys', 'Eco-Friendly Building Blocks', 'Sustainable Puzzle Games',
                'Organic Cotton Stuffed Animals', 'Bamboo Board Games', 'Recycled Plastic Toys',
                'Natural Wood Art Set', 'Eco-Friendly Science Kit', 'Sustainable Musical Instruments',
                'Organic Fabric Dolls', 'Bamboo Learning Toys', 'Recycled Material Craft Kit'
            ],
            'Automotive': [
                'Eco-Friendly Car Wash Kit', 'Sustainable Car Seat Covers', 'Solar Car Charger',
                'Bamboo Car Air Freshener', 'Eco-Friendly Tire Cleaner', 'Sustainable Car Organizer',
                'Natural Car Wax', 'Eco-Friendly Floor Mats', 'Solar Dashboard Camera',
                'Sustainable Car Tools', 'Organic Car Cleaning Cloths', 'Eco-Friendly Engine Oil'
            ]
        }

    def clear_database(self):
        """Clear all existing data from the database"""
        print("🗑️  Clearing existing database data...")
        
        # Order matters due to foreign key constraints
        tables_to_clear = [
            # Clear dependent tables first
            OrderItem, CartItem, Wishlist, ProductReview, ProductImage, ProductVariant,
            RecentlyViewed, AvailabilityNotification, Sales, PriceHistory,
            PostComment, PostLike, PostHashtag, UserInteractionLog, UserBehaviorProfile,
            CouponUsage, SellerCommission, PaymentTransaction, Invoice, Refund,
            RMAItem, RMATimeline, RMADocument, RMAApproval, ReturnShipment,
            TrackingEvent, Shipment, SupportMessage, SupportAttachment,
            InventoryLog, ChannelInventory, InventorySyncLog,
            EmailNotification, SearchAnalytics, VisualSearchAnalytics,
            
            # Clear main tables
            Order, Product, CommunityPost, Hashtag, User, Seller, AdminUser,
            SupportTicket, RMARequest, PaymentGateway, ShippingCarrier,
            Coupon, TaxRate, ShippingZone, ShippingMethod, FulfillmentRule,
            CarrierRate, RMARule, RMAConfiguration, SalesChannel,
            ContentPage, Banner, OAuthProvider, GuestSession, SavedCart,
            AbandonedCart, NewsletterSubscription, CookieConsent,
            DataExportRequest, SellerStore, SellerPayout
        ]
        
        for table in tables_to_clear:
            try:
                db.session.query(table).delete()
                print(f"  ✅ Cleared {table.__name__}")
            except Exception as e:
                print(f"  ⚠️  Warning clearing {table.__name__}: {e}")
        
        db.session.commit()
        print("✅ Database cleared successfully")

    def create_admin_users(self):
        """Create admin users"""
        print("👤 Creating admin users...")
        
        admin_users_data = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'first_name': 'System',
                'last_name': 'Administrator',
                'role': 'super_admin',
                'can_manage_users': True
            },
            {
                'username': 'manager',
                'email': '<EMAIL>',
                'password': 'manager123',
                'first_name': 'Store',
                'last_name': 'Manager',
                'role': 'manager',
                'can_manage_users': False
            },
            {
                'username': 'support',
                'email': '<EMAIL>',
                'password': 'support123',
                'first_name': 'Customer',
                'last_name': 'Support',
                'role': 'support',
                'can_manage_users': False
            }
        ]
        
        from flask_bcrypt import Bcrypt
        bcrypt = Bcrypt()
        
        for admin_data in admin_users_data:
            admin = AdminUser(
                username=admin_data['username'],
                email=admin_data['email'],
                password=bcrypt.generate_password_hash(admin_data['password']).decode('utf-8'),
                first_name=admin_data['first_name'],
                last_name=admin_data['last_name'],
                role=admin_data['role'],
                can_manage_users=admin_data['can_manage_users'],
                is_active=True,
                created_at=datetime.utcnow()
            )
            db.session.add(admin)
            self.created_data['admin_users'].append(admin)
        
        db.session.commit()
        print(f"✅ Created {len(admin_users_data)} admin users")

    def create_users(self, count=50):
        """Create regular users"""
        print(f"👥 Creating {count} users...")
        
        from flask_bcrypt import Bcrypt
        bcrypt = Bcrypt()
        
        for i in range(count):
            fake_name = fake.name().split()
            first_name = fake_name[0] if len(fake_name) > 0 else fake.first_name()
            last_name = fake_name[-1] if len(fake_name) > 1 else fake.last_name()

            user = User(
                username=fake.user_name() + str(i),
                email=fake.email(),
                password=bcrypt.generate_password_hash('password123').decode('utf-8'),
                first_name=first_name,
                last_name=last_name,
                phone=fake.phone_number()[:15],
                date_of_birth=fake.date_of_birth(minimum_age=18, maximum_age=80),
                is_active=True,
                created_at=fake.date_time_between(start_date='-2y', end_date='now')
            )
            db.session.add(user)
            self.created_data['users'].append(user)
        
        db.session.commit()
        print(f"✅ Created {count} users")

    def create_sellers(self, count=20):
        """Create seller accounts"""
        print(f"🏪 Creating {count} sellers...")
        
        for i in range(count):
            business_name = fake.company()
            seller = Seller(
                business_name=business_name,
                contact_person=fake.name(),
                email=fake.company_email(),
                phone=fake.phone_number()[:15],
                business_type=random.choice(['individual', 'partnership', 'company', 'llp']),
                category=random.choice(['Electronics', 'Fashion', 'Home & Garden', 'Sports & Outdoors', 'Books & Media']),
                description=fake.text(max_nb_chars=200),
                address=fake.address(),
                gst_number=f"GST{fake.random_number(digits=12)}",
                pan_number=fake.random_number(digits=10),
                bank_account=fake.bban()[:20],
                ifsc_code=f"BANK{fake.random_number(digits=7)}",
                bank_holder_name=fake.name(),
                store_name=business_name + " Store",
                store_description=fake.text(max_nb_chars=150),
                store_slug=business_name.lower().replace(' ', '-').replace(',', '').replace('.', '') + str(i),
                commission_rate=round(random.uniform(5.0, 15.0), 2),
                is_verified=random.choice([True, False]),
                status=random.choice(['approved', 'pending', 'approved']),  # More approved sellers
                total_earnings=round(random.uniform(500, 25000), 2),
                created_at=fake.date_time_between(start_date='-1y', end_date='now')
            )
            db.session.add(seller)
            self.created_data['sellers'].append(seller)
        
        db.session.commit()
        print(f"✅ Created {count} sellers")

    def create_products(self):
        """Create sustainable products with realistic names and appropriate images"""
        print(f"📦 Creating {self.num_products} sustainable products...")

        # Image mappings for different product types
        image_keywords = {
            'Electronics': ['smartphone', 'laptop', 'tablet', 'headphones', 'camera', 'smartwatch', 'speaker', 'charger'],
            'Fashion': ['tshirt', 'jacket', 'jeans', 'shoes', 'dress', 'sweater', 'bag', 'accessories'],
            'Home & Garden': ['kitchen', 'furniture', 'bedding', 'plants', 'lighting', 'decor', 'utensils', 'towels'],
            'Sports & Outdoors': ['yoga', 'fitness', 'hiking', 'camping', 'sports', 'outdoor', 'exercise', 'bike'],
            'Health & Beauty': ['skincare', 'cosmetics', 'beauty', 'wellness', 'natural', 'organic', 'spa', 'health'],
            'Food & Beverages': ['coffee', 'tea', 'organic', 'healthy', 'snacks', 'nutrition', 'food', 'drinks'],
            'Baby & Kids': ['baby', 'toys', 'children', 'kids', 'nursery', 'playground', 'education', 'family'],
            'Books & Media': ['books', 'reading', 'education', 'learning', 'knowledge', 'study', 'library', 'magazine'],
            'Toys & Games': ['toys', 'games', 'play', 'children', 'fun', 'educational', 'wooden', 'creative'],
            'Automotive': ['car', 'automotive', 'vehicle', 'transport', 'eco', 'green', 'sustainable', 'electric']
        }

        for i in range(self.num_products):
            # Select random category and get sustainable product name
            main_category = random.choice(list(self.categories.keys()))
            brand = random.choice(self.brands.get(main_category, ['EcoChoice', 'GreenLiving', 'Sustainable']))

            # Get realistic sustainable product name
            product_templates = self.sustainable_products.get(main_category, ['Eco-Friendly Product'])
            base_product_name = random.choice(product_templates)

            # Create full product name with brand
            product_name = f"{brand} {base_product_name}"
            if len(product_name) > 100:
                product_name = product_name[:97] + "..."

            # Generate appropriate image URL based on product category
            category_keywords = image_keywords.get(main_category, ['product'])
            image_keyword = random.choice(category_keywords)

            # Use Unsplash for more relevant images
            image_url = f"https://images.unsplash.com/photo-1{random.randint(500000000, 699999999)}-{random.randint(100000000, 999999999)}?w=400&h=400&fit=crop&crop=center&auto=format&q=80"

            # Alternative: Use more specific Picsum with seed based on category
            category_seed = hash(main_category + str(i)) % 1000
            fallback_image = f"https://picsum.photos/400/400?random={category_seed}"

            seller = random.choice(self.created_data['sellers'])

            # Generate sustainable product description
            sustainability_features = [
                "Made from recycled materials", "Carbon-neutral shipping", "Biodegradable packaging",
                "Fair trade certified", "Organic materials", "Renewable energy production",
                "Zero waste manufacturing", "Ethically sourced", "Plastic-free packaging",
                "Sustainably harvested", "Eco-friendly production", "Minimal environmental impact"
            ]

            base_description = fake.text(max_nb_chars=300)
            sustainability_feature = random.choice(sustainability_features)
            description = f"{base_description} {sustainability_feature}. Perfect for environmentally conscious consumers."

            # Set appropriate materials based on category
            sustainable_materials = {
                'Electronics': ['Recycled Aluminum', 'Bioplastic', 'Recycled Steel', 'Sustainable Silicon'],
                'Fashion': ['Organic Cotton', 'Hemp', 'Bamboo Fiber', 'Recycled Polyester', 'Linen', 'Tencel'],
                'Home & Garden': ['Bamboo', 'Recycled Wood', 'Organic Cotton', 'Cork', 'Recycled Glass'],
                'Sports & Outdoors': ['Recycled Polyester', 'Organic Cotton', 'Natural Rubber', 'Hemp'],
                'Health & Beauty': ['Organic Ingredients', 'Natural Materials', 'Biodegradable Components'],
                'Food & Beverages': ['Organic', 'Fair Trade', 'Natural', 'Non-GMO'],
                'Baby & Kids': ['Organic Cotton', 'Natural Wood', 'BPA-Free Materials', 'Non-toxic Materials'],
                'Books & Media': ['Recycled Paper', 'Soy-based Ink', 'Sustainable Materials'],
                'Toys & Games': ['Sustainable Wood', 'Organic Cotton', 'Recycled Plastic', 'Natural Materials'],
                'Automotive': ['Recycled Materials', 'Bio-based Components', 'Sustainable Composites']
            }

            material = random.choice(sustainable_materials.get(main_category, ['Eco-Friendly Materials']))

            product = Product(
                name=product_name,
                price=round(random.uniform(15.0, 1500.0), 2),  # Slightly higher prices for sustainable products
                image=fallback_image,  # Use fallback for now, can be updated later
                sustainability_score=random.randint(70, 100),  # Higher sustainability scores
                description=description,
                category=main_category,
                brand=brand,
                sku=f"ECO-{fake.uuid4()[:8].upper()}",  # ECO prefix for sustainable products
                stock_quantity=random.randint(5, 300),
                weight=round(random.uniform(0.1, 50.0), 2),
                dimensions=f"{random.randint(5,50)}x{random.randint(5,50)}x{random.randint(5,50)} cm",
                material=material,
                care_instructions=f"Eco-friendly care: {fake.sentence()}",
                seller_id=seller.id,
                average_rating=round(random.uniform(4.0, 5.0), 1),  # Higher ratings for quality sustainable products
                total_reviews=random.randint(10, 250),
                low_stock_threshold=random.randint(5, 20),
                created_at=fake.date_time_between(start_date='-1y', end_date='now')
            )
            db.session.add(product)
            self.created_data['products'].append(product)

        # Commit products first to get IDs
        db.session.commit()
        print(f"✅ Created {len(self.created_data['products'])} products")

        # Now create product images and variants
        print("🖼️  Creating product images and variants...")
        for i, product in enumerate(self.created_data['products']):
            # Create product images with category-appropriate seeds
            category_seed_base = hash(product.category) % 1000
            for j in range(random.randint(2, 4)):  # 2-4 images per product
                # Create unique seed for each image based on product and image number
                image_seed = (category_seed_base + i * 10 + j) % 10000

                image = ProductImage(
                    product_id=product.id,
                    image_url=f"https://picsum.photos/800/600?random={image_seed}",
                    alt_text=f"{product.name} - {['Main View', 'Detail View', 'Side View', 'Packaging'][j] if j < 4 else f'Image {j+1}'}",
                    is_primary=(j == 0),
                    display_order=j
                )
                db.session.add(image)

            # Create product variants
            if random.choice([True, False]):
                variant_types = ['size', 'color', 'style']
                for variant_type in random.sample(variant_types, random.randint(1, 2)):
                    if variant_type == 'size':
                        values = ['S', 'M', 'L', 'XL']
                    elif variant_type == 'color':
                        values = ['Red', 'Blue', 'Green', 'Black', 'White']
                    else:
                        values = ['Classic', 'Modern', 'Vintage', 'Premium']

                    for value in random.sample(values, random.randint(2, 4)):
                        variant = ProductVariant(
                            product_id=product.id,
                            variant_type=variant_type,
                            variant_value=value,
                            price_adjustment=round(random.uniform(-50.0, 100.0), 2),
                            stock_quantity=random.randint(0, 100),
                            sku_suffix=value[:3].upper(),
                            is_available=True
                        )
                        db.session.add(variant)

        db.session.commit()
        print(f"✅ Created product images and variants for {len(self.created_data['products'])} products")

    def create_orders_and_reviews(self):
        """Create orders and product reviews"""
        print("🛒 Creating orders and reviews...")

        num_orders = min(200, len(self.created_data['users']) * 3)

        for i in range(num_orders):
            user = random.choice(self.created_data['users'])

            # Create shipping and billing addresses as JSON
            shipping_addr = {
                'full_name': f"{user.first_name} {user.last_name}",
                'address_line_1': fake.street_address(),
                'city': fake.city(),
                'state': fake.state(),
                'postal_code': fake.postcode(),
                'country': 'India',
                'phone': user.phone
            }

            order = Order(
                user_id=user.id,
                order_number=f"ORD-{fake.uuid4()[:8].upper()}",
                status=random.choice(['pending', 'confirmed', 'shipped', 'delivered', 'cancelled']),
                total_amount=0,  # Will calculate after adding items
                subtotal=0,
                tax_amount=0,
                shipping_amount=round(random.uniform(0, 50.0), 2),
                discount_amount=round(random.uniform(0, 100.0), 2),
                payment_status=random.choice(['pending', 'paid', 'failed', 'refunded']),
                payment_method=random.choice(['card', 'upi', 'wallet', 'cod']),
                shipping_address=shipping_addr,
                billing_address=shipping_addr,  # Same as shipping for simplicity
                created_at=fake.date_time_between(start_date='-6m', end_date='now')
            )
            db.session.add(order)
            self.created_data['orders'].append(order)

        # Commit orders first to get IDs
        db.session.commit()
        print(f"✅ Created {len(self.created_data['orders'])} orders")

        # Now create order items
        print("📦 Creating order items...")
        for order in self.created_data['orders']:
            # Add order items
            num_items = random.randint(1, 5)
            order_total = 0

            for j in range(num_items):
                product = random.choice(self.created_data['products'])
                quantity = random.randint(1, 3)
                unit_price = product.price

                order_item = OrderItem(
                    order_id=order.id,
                    product_id=product.id,
                    quantity=quantity,
                    unit_price=unit_price,
                    total_price=unit_price * quantity,
                    product_name=product.name,
                    product_image=product.image
                )
                db.session.add(order_item)
                order_total += unit_price * quantity

            # Update order totals
            order.subtotal = order_total
            order.tax_amount = round(order_total * 0.18, 2)  # 18% tax
            order.total_amount = order.subtotal + order.tax_amount + order.shipping_amount - order.discount_amount

        db.session.commit()
        print(f"✅ Created order items for {len(self.created_data['orders'])} orders")

        # Create product reviews
        print("⭐ Creating product reviews...")
        num_reviews = min(500, len(self.created_data['users']) * 2)

        for i in range(num_reviews):
            user = random.choice(self.created_data['users'])
            product = random.choice(self.created_data['products'])

            # Check if user already reviewed this product
            existing_review = ProductReview.query.filter_by(user_id=user.id, product_id=product.id).first()
            if existing_review:
                continue

            review = ProductReview(
                product_id=product.id,
                user_id=user.id,
                rating=random.randint(1, 5),
                title=fake.sentence(nb_words=6),
                comment=fake.text(max_nb_chars=300),
                verified_purchase=random.choice([True, False]),
                helpful_count=random.randint(0, 50),
                created_at=fake.date_time_between(start_date='-1y', end_date='now')
            )
            db.session.add(review)

        db.session.commit()
        print(f"✅ Created {num_orders} orders and reviews")

    def create_community_content(self):
        """Create community posts, hashtags, and interactions"""
        print("💬 Creating community content...")

        # Create hashtags
        hashtag_names = [
            'fashion', 'electronics', 'deals', 'newproduct', 'review', 'unboxing',
            'style', 'tech', 'home', 'fitness', 'beauty', 'gaming', 'books',
            'food', 'travel', 'diy', 'sustainable', 'luxury', 'budget', 'trending'
        ]

        for tag_name in hashtag_names:
            hashtag = Hashtag(
                tag=tag_name,
                usage_count=random.randint(10, 500),
                created_at=fake.date_time_between(start_date='-1y', end_date='now')
            )
            db.session.add(hashtag)
            self.created_data['hashtags'].append(hashtag)

        db.session.commit()

        # Create community posts
        num_posts = min(300, len(self.created_data['users']) * 2)

        for i in range(num_posts):
            user = random.choice(self.created_data['users'])

            post = CommunityPost(
                user_id=user.id,
                content=fake.text(max_nb_chars=400),
                post_type=random.choice(['text', 'photo', 'review', 'question', 'poll']),
                image_url=f"https://picsum.photos/600/400?random={i+1000}" if random.choice([True, False]) else None,
                likes_count=random.randint(0, 100),
                comments_count=random.randint(0, 50),
                shares_count=random.randint(0, 20),
                created_at=fake.date_time_between(start_date='-6m', end_date='now')
            )
            db.session.add(post)
            self.created_data['posts'].append(post)

        # Commit posts first to get IDs
        db.session.commit()
        print(f"✅ Created {len(self.created_data['posts'])} community posts")

        # Now create hashtags and likes for posts
        print("🏷️  Creating post hashtags and likes...")
        for post in self.created_data['posts']:
            # Add hashtags to posts
            num_hashtags = random.randint(1, 4)
            for hashtag in random.sample(self.created_data['hashtags'], num_hashtags):
                post_hashtag = PostHashtag(
                    post_id=post.id,
                    hashtag_id=hashtag.id,
                    created_at=datetime.utcnow()
                )
                db.session.add(post_hashtag)

            # Add likes
            num_likes = random.randint(0, min(20, len(self.created_data['users'])))
            liked_users = random.sample(self.created_data['users'], num_likes)
            for liked_user in liked_users:
                if liked_user.id != post.user_id:  # Users can't like their own posts
                    like = PostLike(
                        post_id=post.id,
                        user_id=liked_user.id,
                        created_at=fake.date_time_between(start_date=post.created_at, end_date='now')
                    )
                    db.session.add(like)

            # Add comments
            num_comments = random.randint(0, 10)
            for j in range(num_comments):
                commenter = random.choice(self.created_data['users'])
                comment = PostComment(
                    post_id=post.id,
                    user_id=commenter.id,
                    content=fake.sentence(nb_words=random.randint(5, 20)),
                    created_at=fake.date_time_between(start_date=post.created_at, end_date='now')
                )
                db.session.add(comment)

        db.session.commit()
        print("✅ Created community posts, hashtags, and interactions")

    def create_system_data(self):
        """Create system-wide data like payment gateways, shipping, etc."""
        print("⚙️ Creating system configuration data...")

        # Payment Gateways
        payment_gateways = [
            {
                'name': 'razorpay',
                'display_name': 'Razorpay',
                'is_active': True,
                'supported_currencies': ['INR', 'USD'],
                'supported_countries': ['IN', 'US'],
                'api_endpoint': 'https://api.razorpay.com',
                'webhook_url': '/webhooks/razorpay',
                'test_mode': True
            },
            {
                'name': 'stripe',
                'display_name': 'Stripe',
                'is_active': True,
                'supported_currencies': ['USD', 'EUR', 'GBP'],
                'supported_countries': ['US', 'GB', 'CA'],
                'api_endpoint': 'https://api.stripe.com',
                'webhook_url': '/webhooks/stripe',
                'test_mode': True
            },
            {
                'name': 'paypal',
                'display_name': 'PayPal',
                'is_active': False,
                'supported_currencies': ['USD', 'EUR'],
                'supported_countries': ['US', 'GB'],
                'api_endpoint': 'https://api.paypal.com',
                'webhook_url': '/webhooks/paypal',
                'test_mode': True
            }
        ]

        for gateway_data in payment_gateways:
            gateway = PaymentGateway(
                name=gateway_data['name'],
                display_name=gateway_data['display_name'],
                is_active=gateway_data['is_active'],
                is_test_mode=gateway_data['test_mode'],
                supported_currencies=gateway_data['supported_currencies'],
                supported_payment_methods=['card', 'wallet', 'bank_transfer'],
                api_key='test_key_' + gateway_data['name'],
                api_secret='test_secret_' + gateway_data['name'],
                webhook_secret='test_webhook_' + gateway_data['name'],
                processing_fee_percentage=2.9,
                min_amount=1.0,
                max_amount=100000.0,
                created_at=datetime.utcnow()
            )
            db.session.add(gateway)

        # Shipping Carriers
        carriers = [
            {'name': 'FedEx', 'code': 'FEDEX', 'tracking_url': 'https://www.fedex.com/track?tracknumber='},
            {'name': 'UPS', 'code': 'UPS', 'tracking_url': 'https://www.ups.com/track?tracknum='},
            {'name': 'DHL', 'code': 'DHL', 'tracking_url': 'https://www.dhl.com/track?trackingNumber='},
            {'name': 'Blue Dart', 'code': 'BLUEDART', 'tracking_url': 'https://www.bluedart.com/track?trackingNumber='},
            {'name': 'DTDC', 'code': 'DTDC', 'tracking_url': 'https://www.dtdc.in/track?trackingNumber='}
        ]

        for carrier_data in carriers:
            carrier = ShippingCarrier(
                name=carrier_data['name'],
                code=carrier_data['code'],
                api_endpoint=carrier_data['tracking_url'],  # Use tracking_url as api_endpoint
                api_key_encrypted='encrypted_key_' + carrier_data['code'],
                is_active=True,
                supported_services=['standard', 'express', 'overnight'],
                rate_calculation_method='api',
                max_weight_kg=50.0,
                max_dimensions_cm={'length': 100, 'width': 100, 'height': 100},
                domestic_coverage=['IN', 'US', 'GB'],
                international_coverage=['worldwide'],
                created_at=datetime.utcnow()
            )
            db.session.add(carrier)

        # Tax Rates
        tax_rates = [
            {'name': 'GST 18%', 'rate': 0.18, 'country': 'IN', 'state': None},
            {'name': 'GST 12%', 'rate': 0.12, 'country': 'IN', 'state': None},
            {'name': 'GST 5%', 'rate': 0.05, 'country': 'IN', 'state': None},
            {'name': 'Sales Tax', 'rate': 0.08, 'country': 'US', 'state': 'CA'},
            {'name': 'VAT', 'rate': 0.20, 'country': 'GB', 'state': None}
        ]

        for tax_data in tax_rates:
            tax_rate = TaxRate(
                name=tax_data['name'],
                rate=tax_data['rate'],
                country=tax_data['country'],
                state=tax_data['state'],
                is_active=True,
                created_at=datetime.utcnow()
            )
            db.session.add(tax_rate)

        # Coupons
        coupons = [
            {
                'code': 'WELCOME10',
                'name': 'Welcome Discount',
                'description': '10% off on first order',
                'discount_type': 'percentage',
                'discount_value': 10.0,
                'minimum_order_value': 500.0,
                'usage_limit': 1000,
                'user_usage_limit': 1,
                'is_active': True
            },
            {
                'code': 'SAVE50',
                'name': 'Flat 50 Off',
                'description': 'Flat ₹50 off on orders above ₹1000',
                'discount_type': 'fixed',
                'discount_value': 50.0,
                'minimum_order_value': 1000.0,
                'usage_limit': 500,
                'user_usage_limit': 3,
                'is_active': True
            },
            {
                'code': 'FREESHIP',
                'name': 'Free Shipping',
                'description': 'Free shipping on all orders',
                'discount_type': 'free_shipping',
                'discount_value': 0.0,
                'minimum_order_value': 0.0,
                'usage_limit': None,
                'user_usage_limit': None,
                'is_active': True
            }
        ]

        for coupon_data in coupons:
            coupon = Coupon(
                code=coupon_data['code'],
                name=coupon_data['name'],
                description=coupon_data['description'],
                discount_type=coupon_data['discount_type'],
                discount_value=coupon_data['discount_value'],
                minimum_amount=coupon_data['minimum_order_value'],  # Use minimum_amount
                usage_limit=coupon_data['usage_limit'],
                usage_limit_per_user=coupon_data['user_usage_limit'],  # Use usage_limit_per_user
                is_active=coupon_data['is_active'],
                valid_from=datetime.utcnow(),
                valid_until=datetime.utcnow() + timedelta(days=365),
                created_at=datetime.utcnow()
            )
            db.session.add(coupon)

        db.session.commit()
        print("✅ Created system configuration data")

    def create_additional_data(self):
        """Create additional data like user addresses, wishlists, etc."""
        print("📋 Creating additional user data...")

        # Create user addresses
        for user in self.created_data['users'][:30]:  # First 30 users get addresses
            for addr_type in ['shipping', 'billing']:
                address = UserAddress(
                    user_id=user.id,
                    address_type=addr_type,
                    is_default=(addr_type == 'shipping'),
                    full_name=f"{user.first_name} {user.last_name}",
                    phone=user.phone,
                    address_line_1=fake.street_address(),
                    address_line_2=fake.secondary_address() if random.choice([True, False]) else None,
                    city=fake.city(),
                    state=fake.state(),
                    postal_code=fake.postcode(),
                    country=fake.country_code(),
                    created_at=datetime.utcnow()
                )
                db.session.add(address)

        # Create wishlists
        for user in random.sample(self.created_data['users'], min(40, len(self.created_data['users']))):
            num_wishlist_items = random.randint(1, 10)
            wishlist_products = random.sample(self.created_data['products'], num_wishlist_items)

            for product in wishlist_products:
                wishlist = Wishlist(
                    user_id=user.id,
                    product_id=product.id,
                    added_at=fake.date_time_between(start_date='-3m', end_date='now')
                )
                db.session.add(wishlist)

        # Create cart items for some users
        for user in random.sample(self.created_data['users'], min(20, len(self.created_data['users']))):
            num_cart_items = random.randint(1, 5)
            cart_products = random.sample(self.created_data['products'], num_cart_items)

            for product in cart_products:
                cart_item = CartItem(
                    user_id=user.id,
                    product_id=product.id,
                    quantity=random.randint(1, 3),
                    guest_session_id=fake.uuid4(),  # Use guest_session_id
                    created_at=fake.date_time_between(start_date='-1w', end_date='now')
                )
                db.session.add(cart_item)

        # Create user interaction logs
        for i in range(500):  # Create 500 interaction logs
            user = random.choice(self.created_data['users'])
            product = random.choice(self.created_data['products'])

            interaction = UserInteractionLog(
                user_id=user.id,
                product_id=product.id,
                interaction_type=random.choice(['view', 'click', 'add_to_cart', 'purchase', 'wishlist_add']),
                session_id=fake.uuid4(),
                interaction_value=random.uniform(1.0, 5.0),  # Rating or value
                context_data={
                    'page_url': f"/product/{product.id}",
                    'referrer_url': random.choice([None, "https://google.com", "https://facebook.com", "/category/electronics"]),
                    'device_type': random.choice(['desktop', 'mobile', 'tablet'])
                },
                interaction_metadata={
                    'user_agent': fake.user_agent(),
                    'ip_address': fake.ipv4()
                },
                timestamp=fake.date_time_between(start_date='-3m', end_date='now')
            )
            db.session.add(interaction)

        # Create newsletter subscriptions
        for i in range(100):
            subscription = NewsletterSubscription(
                email=fake.email(),
                is_active=random.choice([True, False]),
                subscription_source=random.choice(['website', 'checkout', 'profile']),
                preferences={'marketing': True, 'updates': True, 'offers': random.choice([True, False])},
                created_at=fake.date_time_between(start_date='-1y', end_date='now')
            )
            db.session.add(subscription)

        # Create some content pages
        content_pages = [
            {
                'slug': 'about-us',
                'title': 'About Allora',
                'content': 'Welcome to Allora, your premier destination for quality products and exceptional service.',
                'meta_title': 'About Us - Allora',
                'meta_description': 'Learn more about Allora and our commitment to excellence.'
            },
            {
                'slug': 'privacy-policy',
                'title': 'Privacy Policy',
                'content': 'Your privacy is important to us. This policy explains how we collect and use your data.',
                'meta_title': 'Privacy Policy - Allora',
                'meta_description': 'Read our privacy policy to understand how we protect your data.'
            },
            {
                'slug': 'terms-of-service',
                'title': 'Terms of Service',
                'content': 'These terms govern your use of our platform and services.',
                'meta_title': 'Terms of Service - Allora',
                'meta_description': 'Review our terms of service for using Allora platform.'
            }
        ]

        admin_user = self.created_data['admin_users'][0]
        for page_data in content_pages:
            page = ContentPage(
                slug=page_data['slug'],
                title=page_data['title'],
                content=page_data['content'],
                meta_title=page_data['meta_title'],
                meta_description=page_data['meta_description'],
                is_published=True,
                created_by=admin_user.id,
                updated_by=admin_user.id,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.session.add(page)

        # Create banners
        banners = [
            {
                'title': 'Summer Sale',
                'subtitle': 'Up to 50% off on selected items',
                'image_url': 'https://picsum.photos/1200/400?random=banner1',
                'link_url': '/category/fashion',
                'button_text': 'Shop Now'
            },
            {
                'title': 'New Electronics',
                'subtitle': 'Latest gadgets and tech accessories',
                'image_url': 'https://picsum.photos/1200/400?random=banner2',
                'link_url': '/category/electronics',
                'button_text': 'Explore'
            }
        ]

        for banner_data in banners:
            banner = Banner(
                title=banner_data['title'],
                subtitle=banner_data['subtitle'],
                image_url=banner_data['image_url'],
                link_url=banner_data['link_url'],
                link_text=banner_data['button_text'],  # Use link_text instead of button_text
                position='home_hero',
                is_active=True,
                display_order=1,
                start_date=datetime.utcnow(),
                end_date=datetime.utcnow() + timedelta(days=30),
                created_by=admin_user.id,
                created_at=datetime.utcnow()
            )
            db.session.add(banner)

        db.session.commit()
        print("✅ Created additional user data, content pages, and banners")

    def run(self):
        """Run the complete seeding process"""
        print("🌱 Starting database seeding process...")
        print(f"📊 Configuration: {self.num_products} products, Clear data: {self.clear_data}")

        with app.app_context():
            try:
                if self.clear_data:
                    self.clear_database()

                # Create all tables
                db.create_all()
                print("✅ Database tables created/verified")

                # Seed data in order
                self.create_admin_users()
                self.create_users()
                self.create_sellers()
                self.create_products()
                self.create_orders_and_reviews()
                self.create_community_content()
                self.create_system_data()
                self.create_additional_data()

                print("🎉 Database seeding completed successfully!")
                print(f"📈 Summary:")
                print(f"   - Admin Users: {len(self.created_data['admin_users'])}")
                print(f"   - Users: {len(self.created_data['users'])}")
                print(f"   - Sellers: {len(self.created_data['sellers'])}")
                print(f"   - Products: {len(self.created_data['products'])}")
                print(f"   - Orders: {len(self.created_data['orders'])}")
                print(f"   - Hashtags: {len(self.created_data['hashtags'])}")
                print(f"   - Categories: {len(self.categories)}")
                print(f"   - Brands: {sum(len(brands) for brands in self.brands.values())}")

            except Exception as e:
                print(f"❌ Error during seeding: {e}")
                db.session.rollback()
                raise

def main():
    """Main function to run the seeding script"""
    parser = argparse.ArgumentParser(description='Seed Allora database with sample data')
    parser.add_argument('--clear', action='store_true', help='Clear existing data before seeding')
    parser.add_argument('--products', type=int, default=100, help='Number of products to create')
    
    args = parser.parse_args()
    
    seeder = DatabaseSeeder(num_products=args.products, clear_data=args.clear)
    seeder.run()

if __name__ == '__main__':
    main()
