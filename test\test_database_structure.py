#!/usr/bin/env python3
"""
Comprehensive Database Structure Test Script
===========================================

This script tests every single table and their structure in the database
to ensure that app.py knows the complete database structure and all models
are properly configured.

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import sys
from dotenv import load_dotenv
from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError
import json

# Load environment variables
load_dotenv()

def get_all_model_classes():
    """Get all database model classes from app.py"""
    try:
        from app import app, db
        
        # Import all models from app.py
        from app import (
            # Core models
            User, Product, Order, OrderItem, Seller, AdminUser,
            # Product related
            ProductReview, ProductImage, ProductVariant, PriceHistory,
            # User related
            UserAddress, PaymentMethod, Wishlist, CartItem, UserInteractionLog, UserBehaviorProfile,
            # Community
            CommunityPost, PostComment, PostLike, Hashtag, PostHashtag, CommunityStats,
            # Commerce
            Coupon, CouponUsage, TaxRate, ShippingZone, ShippingMethod,
            # Analytics
            Sales, SearchAnalytics, VisualSearchAnalytics, NewsletterSubscription,
            # Fulfillment
            ShippingCarrier, Shipment, TrackingEvent, FulfillmentRule, CarrierRate,
            # RMA
            RMARequest, RMAItem, RMATimeline, RMADocument, RMAApproval, RMARule, RMAConfiguration, ReturnShipment,
            # Support
            SupportTicket, SupportMessage, SupportAttachment,
            # Payment
            PaymentGateway, PaymentTransaction, Invoice, Refund,
            # Seller
            SellerStore, SellerCommission, SellerPayout,
            # Content
            ContentPage, Banner, RecentlyViewed, AvailabilityNotification,
            # Inventory
            InventoryLog, SalesChannel, ChannelInventory, InventorySyncLog,
            # OAuth
            OAuthProvider, UserOAuth,
            # Other
            EmailNotification, GuestSession, SavedCart, AbandonedCart,
            CookieConsent, DataExportRequest, UserSession, CommunityInsight,
            AdminActivityLog, InventoryConflict, SyncQueue, ProductComparison,
            CookieConsentHistory, CookieAuditLog, RMAStats,
            # New missing models
            Category, SimpleUser, SimpleOrder, SimpleProduct, SimpleSeller,
            ChatSession, ChatMessage, SearchClick, SearchConversion, TestTable
        )
        
        models = {
            # Core models
            'User': User, 'Product': Product, 'Order': Order, 'OrderItem': OrderItem, 
            'Seller': Seller, 'AdminUser': AdminUser,
            # Product related
            'ProductReview': ProductReview, 'ProductImage': ProductImage, 
            'ProductVariant': ProductVariant, 'PriceHistory': PriceHistory,
            # User related
            'UserAddress': UserAddress, 'PaymentMethod': PaymentMethod, 
            'Wishlist': Wishlist, 'CartItem': CartItem, 
            'UserInteractionLog': UserInteractionLog, 'UserBehaviorProfile': UserBehaviorProfile,
            # Community
            'CommunityPost': CommunityPost, 'PostComment': PostComment, 
            'PostLike': PostLike, 'Hashtag': Hashtag, 'PostHashtag': PostHashtag, 
            'CommunityStats': CommunityStats,
            # Commerce
            'Coupon': Coupon, 'CouponUsage': CouponUsage, 'TaxRate': TaxRate, 
            'ShippingZone': ShippingZone, 'ShippingMethod': ShippingMethod,
            # Analytics
            'Sales': Sales, 'SearchAnalytics': SearchAnalytics, 
            'VisualSearchAnalytics': VisualSearchAnalytics, 'NewsletterSubscription': NewsletterSubscription,
            # Fulfillment
            'ShippingCarrier': ShippingCarrier, 'Shipment': Shipment, 
            'TrackingEvent': TrackingEvent, 'FulfillmentRule': FulfillmentRule, 'CarrierRate': CarrierRate,
            # RMA
            'RMARequest': RMARequest, 'RMAItem': RMAItem, 'RMATimeline': RMATimeline, 
            'RMADocument': RMADocument, 'RMAApproval': RMAApproval, 'RMARule': RMARule, 
            'RMAConfiguration': RMAConfiguration, 'ReturnShipment': ReturnShipment,
            # Support
            'SupportTicket': SupportTicket, 'SupportMessage': SupportMessage, 
            'SupportAttachment': SupportAttachment,
            # Payment
            'PaymentGateway': PaymentGateway, 'PaymentTransaction': PaymentTransaction, 
            'Invoice': Invoice, 'Refund': Refund,
            # Seller
            'SellerStore': SellerStore, 'SellerCommission': SellerCommission, 'SellerPayout': SellerPayout,
            # Content
            'ContentPage': ContentPage, 'Banner': Banner, 'RecentlyViewed': RecentlyViewed, 
            'AvailabilityNotification': AvailabilityNotification,
            # Inventory
            'InventoryLog': InventoryLog, 'SalesChannel': SalesChannel, 
            'ChannelInventory': ChannelInventory, 'InventorySyncLog': InventorySyncLog,
            # OAuth
            'OAuthProvider': OAuthProvider, 'UserOAuth': UserOAuth,
            # Other
            'EmailNotification': EmailNotification, 'GuestSession': GuestSession, 
            'SavedCart': SavedCart, 'AbandonedCart': AbandonedCart,
            'CookieConsent': CookieConsent, 'DataExportRequest': DataExportRequest, 
            'UserSession': UserSession, 'CommunityInsight': CommunityInsight,
            'AdminActivityLog': AdminActivityLog, 'InventoryConflict': InventoryConflict, 
            'SyncQueue': SyncQueue, 'ProductComparison': ProductComparison,
            'CookieConsentHistory': CookieConsentHistory, 'CookieAuditLog': CookieAuditLog,
            'RMAStats': RMAStats,
            # New missing models
            'Category': Category, 'SimpleUser': SimpleUser, 'SimpleOrder': SimpleOrder,
            'SimpleProduct': SimpleProduct, 'SimpleSeller': SimpleSeller,
            'ChatSession': ChatSession, 'ChatMessage': ChatMessage,
            'SearchClick': SearchClick, 'SearchConversion': SearchConversion, 'TestTable': TestTable
        }
        
        return app, db, models
        
    except ImportError as e:
        print(f"❌ Failed to import models: {e}")
        return None, None, None
    except Exception as e:
        print(f"❌ Error getting model classes: {e}")
        return None, None, None

def test_table_structure(app, db, model_class, model_name):
    """Test individual table structure"""
    try:
        with app.app_context():
            # Get table name
            table_name = model_class.__tablename__ if hasattr(model_class, '__tablename__') else model_class.__table__.name
            
            # Get inspector
            inspector = inspect(db.engine)
            
            # Check if table exists in database
            if not inspector.has_table(table_name):
                return {
                    'status': 'FAIL',
                    'error': f'Table {table_name} does not exist in database',
                    'table_name': table_name
                }
            
            # Get table columns from database
            db_columns = inspector.get_columns(table_name)
            db_column_names = {col['name'] for col in db_columns}
            
            # Get model columns from SQLAlchemy model
            model_columns = model_class.__table__.columns
            model_column_names = {col.name for col in model_columns}
            
            # Check for missing columns
            missing_in_db = model_column_names - db_column_names
            missing_in_model = db_column_names - model_column_names
            
            # Get foreign keys
            db_foreign_keys = inspector.get_foreign_keys(table_name)
            
            # Get indexes
            db_indexes = inspector.get_indexes(table_name)
            
            # Get primary keys
            db_primary_keys = inspector.get_pk_constraint(table_name)
            
            # Test basic operations
            try:
                # Test count query
                count = db.session.query(model_class).count()
                query_test = 'PASS'
            except Exception as e:
                count = 'ERROR'
                query_test = f'FAIL: {str(e)}'
            
            result = {
                'status': 'PASS' if not missing_in_db and not missing_in_model else 'WARNING',
                'table_name': table_name,
                'model_name': model_name,
                'column_count': len(model_column_names),
                'db_column_count': len(db_column_names),
                'record_count': count,
                'query_test': query_test,
                'missing_in_db': list(missing_in_db) if missing_in_db else None,
                'missing_in_model': list(missing_in_model) if missing_in_model else None,
                'foreign_keys': len(db_foreign_keys),
                'indexes': len(db_indexes),
                'primary_keys': db_primary_keys['constrained_columns'] if db_primary_keys else []
            }
            
            if missing_in_db or missing_in_model:
                result['status'] = 'FAIL'
                
            return result
            
    except Exception as e:
        return {
            'status': 'ERROR',
            'error': str(e),
            'table_name': getattr(model_class, '__tablename__', 'unknown'),
            'model_name': model_name
        }

def main():
    """Main test function"""
    print("🔍 Comprehensive Database Structure Test")
    print("=" * 60)
    
    # Get all models
    app, db, models = get_all_model_classes()
    if not app or not db or not models:
        print("❌ Failed to load models")
        return 1
    
    print(f"📊 Found {len(models)} database models to test")
    print("-" * 60)
    
    results = {}
    passed = 0
    failed = 0
    warnings = 0
    
    # Test each model
    for model_name, model_class in sorted(models.items()):
        print(f"🔍 Testing {model_name}...", end=" ")
        
        result = test_table_structure(app, db, model_class, model_name)
        results[model_name] = result
        
        if result['status'] == 'PASS':
            print("✅ PASS")
            passed += 1
        elif result['status'] == 'WARNING':
            print("⚠️  WARNING")
            warnings += 1
        else:
            print("❌ FAIL")
            failed += 1
            
        # Show details for failed tests
        if result['status'] in ['FAIL', 'ERROR']:
            if 'error' in result:
                print(f"   Error: {result['error']}")
            if 'missing_in_db' in result and result['missing_in_db']:
                print(f"   Missing in DB: {result['missing_in_db']}")
            if 'missing_in_model' in result and result['missing_in_model']:
                print(f"   Missing in Model: {result['missing_in_model']}")
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Passed: {passed}")
    print(f"⚠️  Warnings: {warnings}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Total: {len(models)}")
    
    # Show detailed results for failed tests
    if failed > 0 or warnings > 0:
        print("\n🔍 DETAILED RESULTS")
        print("-" * 40)
        for model_name, result in results.items():
            if result['status'] in ['FAIL', 'ERROR', 'WARNING']:
                print(f"\n{model_name} ({result['status']}):")
                print(f"  Table: {result.get('table_name', 'unknown')}")
                if 'error' in result:
                    print(f"  Error: {result['error']}")
                if 'column_count' in result:
                    print(f"  Columns: {result['column_count']} (Model) vs {result['db_column_count']} (DB)")
                if 'record_count' in result:
                    print(f"  Records: {result['record_count']}")
                if 'query_test' in result and result['query_test'] != 'PASS':
                    print(f"  Query Test: {result['query_test']}")
    
    # Overall result
    if failed == 0:
        print(f"\n🎉 All {len(models)} database models are properly configured!")
        print("✅ Your app.py file perfectly knows the database structure.")
        return 0
    else:
        print(f"\n❌ {failed} models have issues that need to be fixed.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
