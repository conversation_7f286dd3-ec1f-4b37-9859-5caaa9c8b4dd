#!/usr/bin/env python3
"""
Interactive cleanup runner for Allora project
=============================================

This script provides a user-friendly interface to clean all dummy/fake/mock data
from the Allora project before running the proper seeding script.
"""

import os
import sys
import subprocess

def show_banner():
    """Display the cleanup banner"""
    print("🧹 ALLORA PROJECT DATA CLEANUP")
    print("=" * 50)
    print("This tool will help you clean all dummy, fake, and mock data")
    print("from your Allora project before running the proper seeding script.")
    print()

def show_cleanup_options():
    """Show what will be cleaned"""
    print("📋 What will be cleaned:")
    print()
    print("🗄️  DATABASE:")
    print("   • All existing products, users, orders")
    print("   • Community posts, reviews, comments")
    print("   • Admin users, sellers, payment data")
    print("   • Analytics, logs, and tracking data")
    print("   • All 79+ database tables")
    print()
    print("🔧 BACKEND FILES:")
    print("   • Log files (*.log)")
    print("   • Python cache (__pycache__, *.pyc)")
    print("   • Temporary files (*.tmp, *.temp)")
    print("   • Test files")
    print()
    print("🎨 FRONTEND FILES:")
    print("   • Build artifacts (build/, dist/)")
    print("   • Node.js cache (node_modules/.cache)")
    print("   • Log files (npm-debug.log, yarn-error.log)")
    print("   • System files (.DS_Store, Thumbs.db)")
    print()

def get_user_choice():
    """Get user's cleanup preference"""
    print("🎯 Cleanup Options:")
    print("1. Complete Cleanup (Database + Files) - Recommended")
    print("2. Database Only")
    print("3. Files Only")
    print("4. Cancel")
    print()
    
    while True:
        choice = input("Select option (1-4): ").strip()
        if choice in ['1', '2', '3', '4']:
            return choice
        print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")

def confirm_cleanup(cleanup_type):
    """Final confirmation before cleanup"""
    print()
    print("⚠️  FINAL CONFIRMATION")
    print("=" * 30)
    
    if cleanup_type == '1':
        print("You are about to perform a COMPLETE CLEANUP:")
        print("• DELETE ALL DATABASE DATA")
        print("• REMOVE ALL TEMPORARY FILES")
    elif cleanup_type == '2':
        print("You are about to perform a DATABASE CLEANUP:")
        print("• DELETE ALL DATABASE DATA")
        print("• Keep all files intact")
    elif cleanup_type == '3':
        print("You are about to perform a FILES CLEANUP:")
        print("• REMOVE ALL TEMPORARY FILES")
        print("• Keep database data intact")
    
    print()
    print("⚠️  THIS ACTION CANNOT BE UNDONE!")
    print()
    
    confirm = input("Type 'YES' to confirm (anything else to cancel): ").strip()
    return confirm.upper() == 'YES'

def run_cleanup(cleanup_type):
    """Execute the cleanup based on user choice"""
    print()
    print("🚀 Starting cleanup process...")
    print()
    
    # Build command
    cmd = [sys.executable, "clear_all_data.py", "--confirm"]
    
    if cleanup_type == '2':
        cmd.append("--database-only")
    elif cleanup_type == '3':
        cmd.append("--files-only")
    
    try:
        # Run the cleanup script
        result = subprocess.run(cmd, check=True, cwd=os.path.dirname(os.path.abspath(__file__)))
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Cleanup failed with error: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️  Cleanup interrupted by user")
        return False

def show_next_steps():
    """Show what to do after cleanup"""
    print()
    print("🎯 NEXT STEPS:")
    print("=" * 20)
    print("Now that your project is clean, you can seed it with proper data:")
    print()
    print("Option 1 - Interactive Seeding (Recommended):")
    print("  python run_seeder.py")
    print()
    print("Option 2 - Direct Command:")
    print("  python seed_database.py --clear --products=100")
    print()
    print("Option 3 - Custom Amount:")
    print("  python seed_database.py --clear --products=50")
    print()
    print("💡 The seeding script will create:")
    print("   • 100 realistic products (customizable)")
    print("   • 50 users with complete profiles")
    print("   • 20 sellers with business data")
    print("   • 200 orders with realistic patterns")
    print("   • 500 product reviews and ratings")
    print("   • Community content and interactions")
    print("   • System configuration data")
    print()

def main():
    """Main function"""
    show_banner()
    
    # Check if we're in the right directory
    if not os.path.exists('clear_all_data.py'):
        print("❌ Error: Please run this script from the allora/backend directory")
        print("   cd allora/backend")
        print("   python run_cleanup.py")
        return
    
    show_cleanup_options()
    
    choice = get_user_choice()
    
    if choice == '4':
        print("❌ Cleanup cancelled by user")
        return
    
    if not confirm_cleanup(choice):
        print("❌ Cleanup cancelled by user")
        return
    
    success = run_cleanup(choice)
    
    if success:
        show_next_steps()
    else:
        print()
        print("❌ Cleanup failed. Please check the error messages above.")
        print("   You may need to:")
        print("   • Check your database connection")
        print("   • Ensure you have proper permissions")
        print("   • Run from the correct directory")

if __name__ == "__main__":
    main()
