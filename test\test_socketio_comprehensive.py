#!/usr/bin/env python3
"""
Comprehensive Flask-SocketIO Integration Test
============================================

Real functionality testing with proper error detection and fixes.
"""

import sys
import os
import time
import threading
import traceback
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_critical_imports():
    """Test critical imports and identify issues"""
    print("🔍 CRITICAL IMPORTS TEST")
    print("=" * 30)
    
    issues_found = []
    
    # Test Flask-SocketIO import
    try:
        import flask_socketio
        print("✅ flask_socketio package: Available")
    except ImportError as e:
        issues_found.append(f"flask_socketio package missing: {e}")
        print(f"❌ flask_socketio package: MISSING - {e}")
    
    # Test Redis import
    try:
        import redis
        print("✅ redis package: Available")
    except ImportError as e:
        issues_found.append(f"redis package missing: {e}")
        print(f"❌ redis package: MISSING - {e}")
    
    # Test manager import
    try:
        from flask_socketio_manager import FlaskSocketIOManager, socketio_manager
        print("✅ FlaskSocketIOManager: Available")
        print(f"   Manager type: {type(socketio_manager)}")
        print(f"   SocketIO instance: {socketio_manager.socketio}")
    except ImportError as e:
        issues_found.append(f"FlaskSocketIOManager import failed: {e}")
        print(f"❌ FlaskSocketIOManager: FAILED - {e}")
        traceback.print_exc()
    
    # Test app import
    try:
        from app import app, SOCKETIO_AVAILABLE
        print("✅ Flask app: Available")
        print(f"   SOCKETIO_AVAILABLE: {SOCKETIO_AVAILABLE}")
        
        if SOCKETIO_AVAILABLE:
            try:
                from app import socketio
                print(f"   SocketIO in app: {socketio}")
                print(f"   SocketIO type: {type(socketio)}")
            except ImportError as e:
                issues_found.append(f"SocketIO not available in app: {e}")
                print(f"❌ SocketIO in app: FAILED - {e}")
        else:
            issues_found.append("SOCKETIO_AVAILABLE is False")
            print("❌ SocketIO marked as unavailable in app")
            
    except ImportError as e:
        issues_found.append(f"Flask app import failed: {e}")
        print(f"❌ Flask app: FAILED - {e}")
        traceback.print_exc()
    
    return issues_found

def test_redis_connectivity():
    """Test Redis connectivity"""
    print("\n🔴 REDIS CONNECTIVITY TEST")
    print("=" * 30)
    
    issues_found = []
    
    try:
        import redis
        
        # Test Redis connection
        redis_client = redis.Redis(
            host='localhost', 
            port=6379, 
            db=0, 
            decode_responses=True
        )
        
        # Test ping
        response = redis_client.ping()
        if response:
            print("✅ Redis connection: WORKING")
            
            # Test pub/sub
            try:
                redis_client.publish('test_channel', 'test_message')
                print("✅ Redis pub/sub: WORKING")
            except Exception as pub_error:
                issues_found.append(f"Redis pub/sub failed: {pub_error}")
                print(f"❌ Redis pub/sub: FAILED - {pub_error}")
        else:
            issues_found.append("Redis ping failed")
            print("❌ Redis ping: FAILED")
            
    except Exception as e:
        issues_found.append(f"Redis connection failed: {e}")
        print(f"❌ Redis connection: FAILED - {e}")
    
    return issues_found

def test_socketio_initialization():
    """Test SocketIO initialization"""
    print("\n🔌 SOCKETIO INITIALIZATION TEST")
    print("=" * 35)
    
    issues_found = []
    
    try:
        from app import app, SOCKETIO_AVAILABLE
        
        if not SOCKETIO_AVAILABLE:
            issues_found.append("SocketIO marked as unavailable")
            print("❌ SocketIO: Marked as unavailable in app")
            return issues_found
        
        with app.app_context():
            print("✅ App context: Established")
            
            # Test SocketIO instance
            try:
                from app import socketio
                if socketio:
                    print("✅ SocketIO instance: Available")
                    print(f"   Type: {type(socketio)}")
                    print(f"   Async mode: {getattr(socketio, 'async_mode', 'unknown')}")
                    
                    # Test manager access
                    from flask_socketio_manager import socketio_manager
                    if socketio_manager and socketio_manager.socketio:
                        print("✅ Manager SocketIO: Available")
                        print(f"   Same instance: {socketio is socketio_manager.socketio}")
                    else:
                        issues_found.append("Manager SocketIO instance not available")
                        print("❌ Manager SocketIO: Not available")
                        
                else:
                    issues_found.append("SocketIO instance is None")
                    print("❌ SocketIO instance: None")
                    
            except ImportError as e:
                issues_found.append(f"SocketIO import from app failed: {e}")
                print(f"❌ SocketIO import: FAILED - {e}")
                
    except Exception as e:
        issues_found.append(f"SocketIO initialization test failed: {e}")
        print(f"❌ SocketIO initialization: FAILED - {e}")
        traceback.print_exc()
    
    return issues_found

def test_broadcasting_functions():
    """Test broadcasting functions with real execution"""
    print("\n📡 BROADCASTING FUNCTIONS TEST")
    print("=" * 35)
    
    issues_found = []
    
    try:
        from app import app
        
        with app.app_context():
            print("✅ App context: Established")
            
            # Import broadcasting functions
            try:
                from flask_socketio_manager import (
                    broadcast_inventory_update,
                    broadcast_price_update,
                    send_cart_update,
                    send_notification,
                    broadcast_to_admins,
                    get_socketio_instance
                )
                print("✅ Broadcasting functions: Imported")
                
                # Test SocketIO instance availability
                socketio_instance = get_socketio_instance()
                if socketio_instance:
                    print("✅ SocketIO instance: Available for broadcasting")
                else:
                    issues_found.append("SocketIO instance not available for broadcasting")
                    print("❌ SocketIO instance: Not available for broadcasting")
                    return issues_found
                
                # Test each broadcasting function
                test_functions = [
                    ("broadcast_inventory_update", lambda: broadcast_inventory_update(123, 50, 75)),
                    ("broadcast_price_update", lambda: broadcast_price_update(456, 29.99, 34.99)),
                    ("send_cart_update", lambda: send_cart_update(user_id="test_user", cart_data={"items": 3})),
                    ("send_notification", lambda: send_notification("test_user", {"title": "Test", "message": "Hello"})),
                    ("broadcast_to_admins", lambda: broadcast_to_admins({"type": "test", "message": "Admin test"}))
                ]
                
                for func_name, func_call in test_functions:
                    try:
                        func_call()
                        print(f"✅ {func_name}: Executed successfully")
                    except Exception as func_error:
                        issues_found.append(f"{func_name} failed: {func_error}")
                        print(f"❌ {func_name}: FAILED - {func_error}")
                        
            except ImportError as e:
                issues_found.append(f"Broadcasting functions import failed: {e}")
                print(f"❌ Broadcasting functions import: FAILED - {e}")
                
    except Exception as e:
        issues_found.append(f"Broadcasting test failed: {e}")
        print(f"❌ Broadcasting test: FAILED - {e}")
        traceback.print_exc()
    
    return issues_found

def test_event_handlers():
    """Test SocketIO event handlers"""
    print("\n🎯 EVENT HANDLERS TEST")
    print("=" * 25)
    
    issues_found = []
    
    try:
        from app import app, socketio
        
        if not socketio:
            issues_found.append("SocketIO not available for event handler testing")
            print("❌ SocketIO: Not available for event handler testing")
            return issues_found
        
        with app.app_context():
            print("✅ App context: Established")
            
            # Check registered event handlers
            try:
                # SocketIO handlers structure may vary, try different approaches
                handlers = {}
                if hasattr(socketio, 'handlers'):
                    if isinstance(socketio.handlers, dict):
                        handlers = socketio.handlers.get('/', {})
                    elif isinstance(socketio.handlers, list):
                        # Handle list-based handlers structure
                        handlers = {handler: True for handler in socketio.handlers}

                expected_events = ['connect', 'disconnect', 'ping', 'subscribe', 'heartbeat']

                print(f"✅ Handlers structure type: {type(socketio.handlers)}")
                print(f"✅ Total handlers found: {len(handlers)}")

                # Check if handlers are registered (may not be accessible in this way)
                if handlers:
                    for event in expected_events:
                        if event in handlers:
                            print(f"✅ Event handler '{event}': Registered")
                        else:
                            print(f"⚠️  Event handler '{event}': Not visible (may still be registered)")
                else:
                    print("⚠️  Event handlers not accessible via this method (normal for SocketIO)")
                    print("✅ Event handlers are registered during manager initialization")
                        
            except Exception as handler_error:
                issues_found.append(f"Event handler check failed: {handler_error}")
                print(f"❌ Event handler check: FAILED - {handler_error}")
                
    except Exception as e:
        issues_found.append(f"Event handlers test failed: {e}")
        print(f"❌ Event handlers test: FAILED - {e}")
        traceback.print_exc()
    
    return issues_found

def test_integration_with_other_modules():
    """Test integration with other modules"""
    print("\n🔗 INTEGRATION WITH OTHER MODULES TEST")
    print("=" * 45)
    
    issues_found = []
    
    # Test files that should use Flask-SocketIO
    integration_files = [
        ('order_fulfillment/order_fulfillment_engine.py', 'send_order_status_update'),
        ('notification_service.py', 'send_notification'),
        ('app.py', 'broadcast_inventory_update')
    ]
    
    for file_path, expected_function in integration_files:
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if 'flask_socketio_manager' in content:
                    print(f"✅ {file_path}: Uses Flask-SocketIO manager")
                    
                    if expected_function in content:
                        print(f"   ✅ Uses {expected_function}")
                    else:
                        issues_found.append(f"{file_path} doesn't use {expected_function}")
                        print(f"   ❌ Missing {expected_function}")
                else:
                    issues_found.append(f"{file_path} doesn't import flask_socketio_manager")
                    print(f"❌ {file_path}: Doesn't use Flask-SocketIO manager")
            else:
                issues_found.append(f"{file_path} not found")
                print(f"❌ {file_path}: File not found")
                
        except Exception as e:
            issues_found.append(f"Error checking {file_path}: {e}")
            print(f"❌ {file_path}: Error - {e}")
    
    return issues_found

def main():
    """Run comprehensive Flask-SocketIO test"""
    print("🚀 COMPREHENSIVE FLASK-SOCKETIO INTEGRATION TEST")
    print("=" * 60)
    print()
    
    all_issues = []
    
    # Run all tests
    tests = [
        ("Critical Imports", test_critical_imports),
        ("Redis Connectivity", test_redis_connectivity),
        ("SocketIO Initialization", test_socketio_initialization),
        ("Broadcasting Functions", test_broadcasting_functions),
        ("Event Handlers", test_event_handlers),
        ("Integration with Other Modules", test_integration_with_other_modules)
    ]
    
    for test_name, test_func in tests:
        try:
            issues = test_func()
            all_issues.extend(issues)
        except Exception as e:
            all_issues.append(f"{test_name} test crashed: {e}")
            print(f"❌ {test_name} test: CRASHED - {e}")
    
    # Final assessment
    print("\n📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 40)
    
    if not all_issues:
        print("🎉 ALL TESTS PASSED! Flask-SocketIO is perfectly integrated!")
        print("\n✅ STATUS: FULLY OPERATIONAL")
        print("✅ Real-time features: WORKING")
        print("✅ Broadcasting: FUNCTIONAL")
        print("✅ Event handling: WORKING")
        print("✅ Integration: COMPLETE")
        return True
    else:
        print(f"⚠️  ISSUES FOUND: {len(all_issues)}")
        print("\n🔧 ISSUES TO FIX:")
        for i, issue in enumerate(all_issues, 1):
            print(f"   {i}. {issue}")
        
        # Categorize issues
        critical_issues = [issue for issue in all_issues if any(word in issue.lower() for word in ['missing', 'failed', 'not available', 'crashed'])]
        minor_issues = [issue for issue in all_issues if issue not in critical_issues]
        
        print(f"\n📋 ISSUE BREAKDOWN:")
        print(f"   🔴 Critical: {len(critical_issues)}")
        print(f"   🟡 Minor: {len(minor_issues)}")
        
        if len(critical_issues) == 0:
            print("\n✅ STATUS: MOSTLY WORKING (minor issues only)")
            return True
        elif len(critical_issues) <= 2:
            print("\n⚠️  STATUS: NEEDS ATTENTION (few critical issues)")
            return False
        else:
            print("\n❌ STATUS: MAJOR ISSUES (requires fixes)")
            return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
