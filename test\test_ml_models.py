#!/usr/bin/env python3
"""
ML Models Testing Script for Allora E-commerce Platform
======================================================

This script tests all ML models in the models folder to check if they're working properly
and identifies any implementation needs.

Models to test:
1. Recommendation Model (recommendation_model.py & recommendation_model.pkl)
2. Visual Search Model (visual_search_model.py & image_features.pkl)
3. Inventory Prediction Model (inventory_prediction_model.py & inventory_predictions.pkl)
4. Price Trend Model (price_trend_model.py & price_trend_predictions.pkl)

Author: Allora Development Team
Date: 2025-07-09
"""

import os
import sys
import pickle
import logging
from datetime import datetime
import traceback

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_file_existence():
    """Test if all model files exist"""
    print("🔍 TESTING MODEL FILE EXISTENCE")
    print("=" * 60)
    
    model_files = {
        "Recommendation Model (Python)": "models/recommendation_model.py",
        "Recommendation Model (Pickle)": "models/recommendation_model.pkl",
        "Visual Search Model (Python)": "models/visual_search_model.py", 
        "Visual Search Features (Pickle)": "models/image_features.pkl",
        "Inventory Prediction Model (Python)": "models/inventory_prediction_model.py",
        "Inventory Predictions (Pickle)": "models/inventory_predictions.pkl",
        "Price Trend Model (Python)": "models/price_trend_model.py",
        "Price Trend Predictions (Pickle)": "models/price_trend_predictions.pkl"
    }
    
    results = {}
    for name, path in model_files.items():
        exists = os.path.exists(path)
        status = "✅ EXISTS" if exists else "❌ MISSING"
        print(f"{name}: {status}")
        results[name] = exists
    
    return results

def test_pickle_files():
    """Test if pickle files can be loaded"""
    print("\n🥒 TESTING PICKLE FILE LOADING")
    print("=" * 60)
    
    pickle_files = {
        "Recommendation Model": "models/recommendation_model.pkl",
        "Visual Search Features": "models/image_features.pkl", 
        "Inventory Predictions": "models/inventory_predictions.pkl",
        "Price Trend Predictions": "models/price_trend_predictions.pkl"
    }
    
    results = {}
    for name, path in pickle_files.items():
        try:
            if os.path.exists(path):
                with open(path, 'rb') as f:
                    data = pickle.load(f)
                
                # Check data structure
                if name == "Recommendation Model":
                    print(f"✅ {name}: Loaded successfully - Shape: {data.shape if hasattr(data, 'shape') else 'Unknown'}")
                elif name == "Visual Search Features":
                    features = data.get('features', [])
                    product_ids = data.get('product_ids', [])
                    print(f"✅ {name}: Loaded successfully - {len(features)} features, {len(product_ids)} product IDs")
                elif name == "Inventory Predictions":
                    print(f"✅ {name}: Loaded successfully - {len(data)} predictions")
                elif name == "Price Trend Predictions":
                    print(f"✅ {name}: Loaded successfully - {len(data)} predictions")
                
                results[name] = True
            else:
                print(f"❌ {name}: File not found")
                results[name] = False
                
        except Exception as e:
            print(f"❌ {name}: Failed to load - {str(e)}")
            results[name] = False
    
    return results

def test_recommendation_model():
    """Test recommendation model functionality"""
    print("\n🎯 TESTING RECOMMENDATION MODEL")
    print("=" * 60)
    
    try:
        # Test if we can load and use the recommendation model
        if os.path.exists("models/recommendation_model.pkl"):
            with open("models/recommendation_model.pkl", 'rb') as f:
                item_similarity_df = pickle.load(f)
            
            print(f"✅ Recommendation model loaded - Shape: {item_similarity_df.shape}")
            
            # Test similarity calculation
            if hasattr(item_similarity_df, 'iloc') and len(item_similarity_df) > 0:
                # Test getting recommendations for product ID 1
                if 1 in item_similarity_df.columns:
                    scores = item_similarity_df[1] * 5  # Mock rating of 5
                    recommended_ids = scores.argsort()[::-1][:3]
                    print(f"✅ Sample recommendations for product 1: {list(recommended_ids)}")
                    return True
                else:
                    print("⚠️ Product ID 1 not found in similarity matrix")
                    return False
            else:
                print("❌ Invalid similarity matrix structure")
                return False
        else:
            print("❌ Recommendation model file not found")
            return False
            
    except Exception as e:
        print(f"❌ Recommendation model test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_visual_search_model():
    """Test visual search model functionality"""
    print("\n🖼️ TESTING VISUAL SEARCH MODEL")
    print("=" * 60)
    
    try:
        # Test if we can load visual search features
        if os.path.exists("models/image_features.pkl"):
            with open("models/image_features.pkl", 'rb') as f:
                visual_search_data = pickle.load(f)
            
            features = visual_search_data.get('features', [])
            product_ids = visual_search_data.get('product_ids', [])
            
            print(f"✅ Visual search features loaded - {len(features)} features, {len(product_ids)} product IDs")
            
            if len(features) > 0 and len(product_ids) > 0:
                # Test feature structure
                first_feature = features[0]
                print(f"✅ Feature vector shape: {first_feature.shape if hasattr(first_feature, 'shape') else len(first_feature)}")
                
                # Test if we can compute similarity (mock test)
                import numpy as np
                if len(features) >= 2:
                    feat1 = np.array(features[0])
                    feat2 = np.array(features[1])
                    
                    # Compute cosine similarity
                    norm1 = np.linalg.norm(feat1)
                    norm2 = np.linalg.norm(feat2)
                    
                    if norm1 > 0 and norm2 > 0:
                        similarity = np.dot(feat1, feat2) / (norm1 * norm2)
                        print(f"✅ Sample similarity between products {product_ids[0]} and {product_ids[1]}: {similarity:.4f}")
                        return True
                    else:
                        print("⚠️ Zero norm features detected")
                        return False
                else:
                    print("⚠️ Not enough features for similarity test")
                    return len(features) > 0
            else:
                print("❌ No features or product IDs found")
                return False
        else:
            print("❌ Visual search features file not found")
            return False
            
    except Exception as e:
        print(f"❌ Visual search model test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_inventory_prediction_model():
    """Test inventory prediction model functionality"""
    print("\n📦 TESTING INVENTORY PREDICTION MODEL")
    print("=" * 60)
    
    try:
        if os.path.exists("models/inventory_predictions.pkl"):
            with open("models/inventory_predictions.pkl", 'rb') as f:
                predictions = pickle.load(f)
            
            print(f"✅ Inventory predictions loaded - {len(predictions)} predictions")
            
            if len(predictions) > 0:
                # Check prediction structure
                sample_prediction = predictions[0]
                required_fields = ['product_id', 'product_name', 'current_stock', 'predicted_sales_next_7_days', 'recommended_restock']
                
                missing_fields = [field for field in required_fields if field not in sample_prediction]
                if not missing_fields:
                    print("✅ Prediction structure is valid")
                    print(f"✅ Sample prediction: Product {sample_prediction['product_id']} - Current: {sample_prediction['current_stock']}, Predicted sales: {sample_prediction['predicted_sales_next_7_days']}, Restock: {sample_prediction['recommended_restock']}")
                    return True
                else:
                    print(f"❌ Missing fields in predictions: {missing_fields}")
                    return False
            else:
                print("❌ No predictions found")
                return False
        else:
            print("❌ Inventory predictions file not found")
            return False
            
    except Exception as e:
        print(f"❌ Inventory prediction model test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_price_trend_model():
    """Test price trend model functionality"""
    print("\n💰 TESTING PRICE TREND MODEL")
    print("=" * 60)
    
    try:
        if os.path.exists("models/price_trend_predictions.pkl"):
            with open("models/price_trend_predictions.pkl", 'rb') as f:
                predictions = pickle.load(f)
            
            print(f"✅ Price trend predictions loaded - {len(predictions)} predictions")
            
            if len(predictions) > 0:
                # Check prediction structure
                sample_prediction = predictions[0]
                required_fields = ['product_id', 'product_name', 'current_price', 'predicted_percentage_change', 'trend']
                
                missing_fields = [field for field in required_fields if field not in sample_prediction]
                if not missing_fields:
                    print("✅ Prediction structure is valid")
                    print(f"✅ Sample prediction: Product {sample_prediction['product_id']} - Current price: ${sample_prediction['current_price']}, Change: {sample_prediction['predicted_percentage_change']}%, Trend: {sample_prediction['trend']}")
                    return True
                else:
                    print(f"❌ Missing fields in predictions: {missing_fields}")
                    return False
            else:
                print("❌ No predictions found")
                return False
        else:
            print("❌ Price trend predictions file not found")
            return False
            
    except Exception as e:
        print(f"❌ Price trend model test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_tensorflow_availability():
    """Test if TensorFlow is available for visual search"""
    print("\n🧠 TESTING TENSORFLOW AVAILABILITY")
    print("=" * 60)
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow version: {tf.__version__}")
        
        # Test MobileNetV2 loading
        model = tf.keras.applications.MobileNetV2(weights='imagenet', include_top=False, pooling='avg')
        print("✅ MobileNetV2 model loaded successfully")
        
        # Test prediction with dummy data
        import numpy as np
        dummy_input = np.random.random((1, 224, 224, 3))
        features = model.predict(dummy_input)
        print(f"✅ Model prediction test successful - Feature shape: {features.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ TensorFlow test failed: {str(e)}")
        return False

def main():
    """Run all ML model tests"""
    print("🚀 ALLORA ML MODELS TESTING")
    print("=" * 80)
    print(f"📅 Test Date: {datetime.now().isoformat()}")
    print("=" * 80)
    
    # Run all tests
    file_results = test_file_existence()
    pickle_results = test_pickle_files()
    recommendation_result = test_recommendation_model()
    visual_search_result = test_visual_search_model()
    inventory_result = test_inventory_prediction_model()
    price_trend_result = test_price_trend_model()
    tensorflow_result = test_tensorflow_availability()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 60)
    
    total_tests = 7
    passed_tests = sum([
        all(file_results.values()),
        all(pickle_results.values()),
        recommendation_result,
        visual_search_result,
        inventory_result,
        price_trend_result,
        tensorflow_result
    ])
    
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED - ML MODELS ARE WORKING PROPERLY!")
    else:
        print("⚠️ SOME TESTS FAILED - IMPLEMENTATION NEEDED")
        
        # Identify what needs implementation
        print("\n🔧 IMPLEMENTATION NEEDS:")
        if not all(file_results.values()):
            print("- Missing model files need to be created")
        if not all(pickle_results.values()):
            print("- Pickle files need to be regenerated")
        if not recommendation_result:
            print("- Recommendation model needs fixing")
        if not visual_search_result:
            print("- Visual search model needs fixing")
        if not inventory_result:
            print("- Inventory prediction model needs fixing")
        if not price_trend_result:
            print("- Price trend model needs fixing")
        if not tensorflow_result:
            print("- TensorFlow installation/configuration needs fixing")

if __name__ == "__main__":
    main()
