#!/usr/bin/env python3
"""
Test script to verify the sort functionality in the API
"""

import requests
import json

API_BASE_URL = "http://localhost:5000/api"

def test_sort_functionality():
    """Test different sort options"""
    print("Testing Sort Functionality...")
    print("=" * 50)
    
    sort_options = [
        ('price', 'asc', 'Price (Low to High)'),
        ('price', 'desc', 'Price (High to Low)'),
        ('rating', 'desc', 'Rating (Highest First)'),
        ('rating', 'asc', 'Rating (Lowest First)'),
        ('popularity', 'desc', 'Popularity (Most Reviews First)'),
        ('popularity', 'asc', 'Popularity (Least Reviews First)'),
        ('sustainability', 'desc', 'Sustainability (Highest First)'),
        ('newest', 'desc', 'Newest First'),
        ('name', 'asc', 'Name (A-Z)')
    ]
    
    for sort_by, sort_order, description in sort_options:
        print(f"\n🔍 Testing: {description}")
        print("-" * 30)
        
        try:
            # Make API request
            response = requests.get(f"{API_BASE_URL}/products", params={
                'sort_by': sort_by,
                'sort_order': sort_order,
                'per_page': 5  # Limit to 5 products for testing
            })
            
            if response.status_code == 200:
                data = response.json()
                products = data.get('products', [])
                
                print(f"✅ API Response: {len(products)} products")
                
                # Display first 3 products with sort-relevant info
                for i, product in enumerate(products[:3], 1):
                    name = product.get('name', 'Unknown')[:30]
                    price = product.get('price', 0)
                    rating = product.get('average_rating', 0)
                    reviews = product.get('total_reviews', 0)
                    sustainability = product.get('sustainabilityScore', 0)
                    
                    if sort_by == 'price':
                        print(f"  {i}. {name} - ₹{price:.0f}")
                    elif sort_by == 'rating':
                        print(f"  {i}. {name} - {rating}⭐ ({reviews} reviews)")
                    elif sort_by == 'popularity':
                        print(f"  {i}. {name} - {reviews} reviews ({rating}⭐)")
                    elif sort_by == 'sustainability':
                        print(f"  {i}. {name} - {sustainability}% sustainable")
                    else:
                        print(f"  {i}. {name}")
                
                # Verify sort order
                if sort_by == 'price' and len(products) >= 2:
                    prices = [p.get('price', 0) for p in products]
                    is_sorted = all(prices[i] <= prices[i+1] for i in range(len(prices)-1)) if sort_order == 'asc' else all(prices[i] >= prices[i+1] for i in range(len(prices)-1))
                    print(f"  📊 Sort verification: {'✅ Correct' if is_sorted else '❌ Incorrect'}")
                
                elif sort_by == 'rating' and len(products) >= 2:
                    ratings = [p.get('average_rating', 0) for p in products]
                    is_sorted = all(ratings[i] >= ratings[i+1] for i in range(len(ratings)-1)) if sort_order == 'desc' else all(ratings[i] <= ratings[i+1] for i in range(len(ratings)-1))
                    print(f"  📊 Sort verification: {'✅ Correct' if is_sorted else '❌ Incorrect'}")
                
                elif sort_by == 'popularity' and len(products) >= 2:
                    reviews = [p.get('total_reviews', 0) for p in products]
                    is_sorted = all(reviews[i] >= reviews[i+1] for i in range(len(reviews)-1)) if sort_order == 'desc' else all(reviews[i] <= reviews[i+1] for i in range(len(reviews)-1))
                    print(f"  📊 Sort verification: {'✅ Correct' if is_sorted else '❌ Incorrect'}")
                    
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"   Response: {response.text[:100]}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Connection Error: {str(e)}")
        except Exception as e:
            print(f"❌ Unexpected Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("Sort functionality test completed!")

if __name__ == "__main__":
    test_sort_functionality()
