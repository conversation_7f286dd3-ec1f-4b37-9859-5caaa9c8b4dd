#!/usr/bin/env python3
"""
Comprehensive Elasticsearch Testing and Analysis for Allora E-commerce
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
import logging
from datetime import datetime
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_elasticsearch_connection():
    """Test basic Elasticsearch connection"""
    print("🔌 ELASTICSEARCH CONNECTION TEST")
    print("="*80)

    try:
        from elasticsearch_config import get_elasticsearch_client

        es_client = get_elasticsearch_client()

        if es_client is None:
            print("❌ Elasticsearch client: NOT AVAILABLE")
            return False

        # Test connection
        if es_client.ping():
            print("✅ Elasticsearch connection: WORKING")

            # Get cluster info
            info = es_client.info()
            print(f"✅ Elasticsearch version: {info['version']['number']}")
            print(f"✅ Cluster name: {info['cluster_name']}")

            # Get cluster health
            health = es_client.cluster.health()
            print(f"✅ Cluster status: {health['status']}")
            print(f"✅ Number of nodes: {health['number_of_nodes']}")

            return True
        else:
            print("❌ Elasticsearch connection: FAILED")
            return False

    except Exception as e:
        print(f"❌ Elasticsearch connection error: {e}")
        return False

def test_elasticsearch_indices():
    """Test Elasticsearch indices"""
    print("\n📊 ELASTICSEARCH INDICES TEST")
    print("="*80)

    try:
        from elasticsearch_config import get_elasticsearch_client, get_product_index_name, get_search_analytics_index_name

        es_client = get_elasticsearch_client()

        if es_client is None:
            print("❌ Elasticsearch client: NOT AVAILABLE")
            return False

        # Get all indices
        indices = es_client.indices.get_alias(index="*")

        print(f"📋 Total indices: {len(indices)}")

        # Check for Allora-specific indices
        allora_indices = [idx for idx in indices.keys() if 'allora' in idx.lower()]

        if allora_indices:
            print("✅ Allora-specific indices found:")
            for idx in allora_indices:
                try:
                    doc_count = es_client.count(index=idx)['count']
                    print(f"  - {idx}: {doc_count} documents")
                except:
                    print(f"  - {idx}: Index exists but count failed")
        else:
            print("⚠️  No Allora-specific indices found")

        # Check for expected indices
        expected_indices = [get_product_index_name(), get_search_analytics_index_name()]
        for idx in expected_indices:
            if idx in indices:
                try:
                    doc_count = es_client.count(index=idx)['count']
                    print(f"✅ {idx} index: {doc_count} documents")
                except:
                    print(f"✅ {idx} index: EXISTS (count failed)")
            else:
                print(f"⚠️  {idx} index: NOT FOUND")

        return len(indices) > 0

    except Exception as e:
        print(f"❌ Elasticsearch indices error: {e}")
        return False

def test_search_functionality():
    """Test search functionality"""
    print("\n🔍 SEARCH FUNCTIONALITY TEST")
    print("="*80)

    try:
        from elasticsearch_search import get_search_engine

        # Initialize search engine
        search_engine = get_search_engine()

        # Test product search
        print("Testing product search...")

        # Search for common terms
        test_queries = ['shirt', 'eco', 'sustainable', 'organic']

        for query in test_queries:
            try:
                results = search_engine.search_products(query=query, per_page=5)

                if 'products' in results:
                    products = results['products']
                    print(f"✅ Search '{query}': {len(products)} results")

                    if products:
                        # Show first result
                        first_result = products[0]
                        print(f"  - Top result: {first_result.get('name', 'N/A')}")
                else:
                    print(f"⚠️  Search '{query}': No products in response")

            except Exception as e:
                print(f"❌ Search '{query}' failed: {e}")

        return True

    except Exception as e:
        print(f"❌ Search functionality error: {e}")
        return False

def test_analytics_integration():
    """Test analytics integration with Elasticsearch"""
    print("\n📈 ANALYTICS INTEGRATION TEST")
    print("="*80)

    try:
        from search_analytics_tracker import SearchAnalyticsTracker

        # Initialize analytics tracker
        tracker = SearchAnalyticsTracker()

        # Test tracking a search
        test_search_data = {
            'search_query': 'test elasticsearch search',
            'user_id': 1,
            'results_count': 5,
            'filters_applied': {'category': 'electronics'},
            'timestamp': datetime.now()
        }

        # Track the search (using correct parameter names)
        tracker.track_search(
            search_query=test_search_data['search_query'],
            user_id=test_search_data['user_id'],
            results_count=test_search_data['results_count'],
            filters_applied=test_search_data['filters_applied']
        )

        print("✅ Search analytics tracking: WORKING")

        # Test getting analytics
        analytics = tracker.get_search_analytics(days=7)
        print(f"✅ Analytics retrieval: {len(analytics)} records")

        return True

    except Exception as e:
        print(f"❌ Analytics integration error: {e}")
        return False

def test_visual_search():
    """Test visual search functionality"""
    print("\n🖼️  VISUAL SEARCH TEST")
    print("="*80)
    
    try:
        # Check if visual search models exist
        model_files = [
            'models/image_features.pkl',
            'models/visual_search_model.py'
        ]
        
        visual_search_available = True
        for model_file in model_files:
            if os.path.exists(model_file):
                print(f"✅ {model_file}: EXISTS")
            else:
                print(f"⚠️  {model_file}: NOT FOUND")
                visual_search_available = False
        
        if visual_search_available:
            print("✅ Visual search models: AVAILABLE")
        else:
            print("⚠️  Visual search models: INCOMPLETE")
        
        return visual_search_available
        
    except Exception as e:
        print(f"❌ Visual search test error: {e}")
        return False

def test_recommendation_integration():
    """Test recommendation system integration"""
    print("\n🎯 RECOMMENDATION SYSTEM TEST")
    print("="*80)

    try:
        # Check if recommendation system is available
        try:
            from recommendation_engine import RecommendationEngine

            # Initialize recommendation engine
            rec_engine = RecommendationEngine()

            # Test getting recommendations for a user
            test_user_id = 1
            recommendations = rec_engine.get_user_recommendations(user_id=test_user_id, limit=5)

            if recommendations:
                print(f"✅ User recommendations: {len(recommendations)} items")
                if recommendations:
                    print(f"  - Sample recommendation: {recommendations[0].get('name', 'N/A')}")
            else:
                print("⚠️  User recommendations: No results")

            # Test product similarity
            test_product_id = 1
            similar_products = rec_engine.get_similar_products(product_id=test_product_id, limit=5)

            if similar_products:
                print(f"✅ Similar products: {len(similar_products)} items")
            else:
                print("⚠️  Similar products: No results")

            return True

        except ImportError:
            print("⚠️  Recommendation system: Module not available")
            return False

    except Exception as e:
        print(f"❌ Recommendation system error: {e}")
        return False

def check_elasticsearch_configuration():
    """Check Elasticsearch configuration"""
    print("\n⚙️  ELASTICSEARCH CONFIGURATION")
    print("="*80)
    
    try:
        # Check configuration files
        config_files = [
            'elasticsearch_config.py',
            'elasticsearch_manager.py',
            'elasticsearch_search.py',
            'elasticsearch_setup_guide.md'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"✅ {config_file}: EXISTS")
            else:
                print(f"❌ {config_file}: MISSING")
        
        # Check environment variables
        es_host = os.getenv('ELASTICSEARCH_HOST', 'localhost:9200')
        print(f"📍 Elasticsearch host: {es_host}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration check error: {e}")
        return False

def generate_elasticsearch_report():
    """Generate comprehensive Elasticsearch report"""
    print("\n🎯 ELASTICSEARCH COMPREHENSIVE REPORT")
    print("="*80)
    
    # Run all tests
    connection_ok = test_elasticsearch_connection()
    indices_ok = test_elasticsearch_indices()
    search_ok = test_search_functionality()
    analytics_ok = test_analytics_integration()
    visual_search_ok = test_visual_search()
    recommendations_ok = test_recommendation_integration()
    config_ok = check_elasticsearch_configuration()
    
    # Calculate overall score
    tests = [connection_ok, indices_ok, search_ok, analytics_ok, visual_search_ok, recommendations_ok, config_ok]
    passed_tests = sum(tests)
    total_tests = len(tests)
    score = (passed_tests / total_tests) * 100
    
    print(f"\n🏆 ELASTICSEARCH IMPLEMENTATION SUMMARY")
    print("="*80)
    print(f"🔌 Connection: {'✅ WORKING' if connection_ok else '❌ FAILED'}")
    print(f"📊 Indices: {'✅ WORKING' if indices_ok else '❌ FAILED'}")
    print(f"🔍 Search: {'✅ WORKING' if search_ok else '❌ FAILED'}")
    print(f"📈 Analytics: {'✅ WORKING' if analytics_ok else '❌ FAILED'}")
    print(f"🖼️  Visual Search: {'✅ WORKING' if visual_search_ok else '❌ FAILED'}")
    print(f"🎯 Recommendations: {'✅ WORKING' if recommendations_ok else '❌ FAILED'}")
    print(f"⚙️  Configuration: {'✅ WORKING' if config_ok else '❌ FAILED'}")
    
    print(f"\n📊 OVERALL SCORE: {passed_tests}/{total_tests} ({score:.1f}%)")
    
    # Final verdict
    if score >= 90:
        verdict = "🎉 EXCELLENT - Fully functional!"
    elif score >= 75:
        verdict = "✅ GOOD - Minor issues"
    elif score >= 50:
        verdict = "⚠️  PARTIAL - Needs attention"
    else:
        verdict = "❌ POOR - Major issues"
    
    print(f"🎯 VERDICT: {verdict}")
    
    return {
        'score': score,
        'tests_passed': passed_tests,
        'total_tests': total_tests,
        'verdict': verdict,
        'details': {
            'connection': connection_ok,
            'indices': indices_ok,
            'search': search_ok,
            'analytics': analytics_ok,
            'visual_search': visual_search_ok,
            'recommendations': recommendations_ok,
            'configuration': config_ok
        }
    }

def main():
    print("🚀 Starting Comprehensive Elasticsearch Analysis...")
    print(f"⏰ Analysis started at: {datetime.now()}")
    
    # Generate comprehensive report
    report = generate_elasticsearch_report()
    
    # Save report
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'elasticsearch_analysis': report
    }
    
    with open('elasticsearch_comprehensive_report.json', 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: elasticsearch_comprehensive_report.json")
    print(f"⏰ Analysis completed at: {datetime.now()}")

if __name__ == '__main__':
    main()
