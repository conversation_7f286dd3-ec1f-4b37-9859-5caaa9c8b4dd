#!/usr/bin/env python3
"""
Remove Seed Files Script
This script will remove all seed/sample data files to prevent accidental product creation.
"""

import os
import shutil
from pathlib import Path

def remove_seed_files():
    """Remove all seed and sample data files"""
    
    # List of seed files to remove
    seed_files = [
        "seed.py",
        "add_sample_products.py", 
        "seed_products.py",
        "Other Files/Setup Files (One-time use)/seed_products.py",
        "Other Files/Setup Files (One-time use)/add_sample_products.py"
    ]
    
    # List of directories that might contain seed files
    seed_directories = [
        "Other Files/Setup Files (One-time use)",
        "seeds",
        "fixtures", 
        "sample_data"
    ]
    
    print("🗑️  Removing seed files and directories...")
    print("=" * 50)
    
    removed_files = []
    removed_dirs = []
    
    # Remove specific seed files
    for file_path in seed_files:
        full_path = Path(file_path)
        if full_path.exists():
            try:
                if full_path.is_file():
                    full_path.unlink()
                    removed_files.append(str(full_path))
                    print(f"✅ Removed file: {file_path}")
                elif full_path.is_dir():
                    shutil.rmtree(full_path)
                    removed_dirs.append(str(full_path))
                    print(f"✅ Removed directory: {file_path}")
            except Exception as e:
                print(f"❌ Failed to remove {file_path}: {e}")
        else:
            print(f"ℹ️  File not found: {file_path}")
    
    # Remove seed directories
    for dir_path in seed_directories:
        full_path = Path(dir_path)
        if full_path.exists() and full_path.is_dir():
            try:
                shutil.rmtree(full_path)
                removed_dirs.append(str(full_path))
                print(f"✅ Removed directory: {dir_path}")
            except Exception as e:
                print(f"❌ Failed to remove directory {dir_path}: {e}")
    
    print(f"\n📊 Summary:")
    print(f"   - Files removed: {len(removed_files)}")
    print(f"   - Directories removed: {len(removed_dirs)}")
    
    if removed_files:
        print(f"\n📄 Removed files:")
        for file in removed_files:
            print(f"   - {file}")
    
    if removed_dirs:
        print(f"\n📁 Removed directories:")
        for dir in removed_dirs:
            print(f"   - {dir}")
    
    if not removed_files and not removed_dirs:
        print("ℹ️  No seed files found to remove.")
    else:
        print("\n✅ Seed file cleanup completed!")
        print("⚠️  Note: This prevents accidental product creation from seed files.")

if __name__ == "__main__":
    print("🧹 Seed Files Cleanup Tool")
    print("=" * 40)
    remove_seed_files()
