#!/usr/bin/env python3
"""
Visual Search Response Format Test
=================================

This script tests the visual search API response format to ensure
the frontend receives the correct data structure.
"""

import requests
import json
import os
from PIL import Image
import io

def create_test_image():
    """Create a simple test image"""
    # Create a simple colored image
    img = Image.new('RGB', (224, 224), color='red')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    return img_bytes

def test_visual_search_response():
    """Test visual search API response format"""
    print("🔍 TESTING VISUAL SEARCH API RESPONSE FORMAT")
    print("=" * 60)
    
    try:
        # Create test image
        test_image = create_test_image()
        
        # Test the visual search endpoint
        files = {'image': ('test.jpg', test_image, 'image/jpeg')}
        response = requests.post('http://localhost:5000/api/visual_search', files=files, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Visual Search API Response:")
            print(f"   - Status Code: {response.status_code}")
            print(f"   - Success: {data.get('success', False)}")
            
            # Check for similar_products key
            if 'similar_products' in data:
                products = data['similar_products']
                print(f"   - Similar Products Found: {len(products)}")
                
                if products:
                    print("   - First Product Fields:")
                    first_product = products[0]
                    for key, value in first_product.items():
                        print(f"     * {key}: {value}")
                
                print("✅ Response format is correct for frontend!")
                return True
            else:
                print("❌ Missing 'similar_products' key in response")
                print("   Available keys:", list(data.keys()))
                return False
                
        else:
            print(f"❌ API returned status code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_production_visual_search():
    """Test production visual search endpoint"""
    print("\n🔍 TESTING PRODUCTION VISUAL SEARCH API")
    print("=" * 60)
    
    try:
        # Create test image
        test_image = create_test_image()
        
        # Test the production visual search endpoint
        files = {'image': ('test.jpg', test_image, 'image/jpeg')}
        response = requests.post('http://localhost:5000/api/smart-features/visual-search', files=files, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Production Visual Search API Response:")
            print(f"   - Status Code: {response.status_code}")
            print(f"   - Success: {data.get('success', False)}")
            
            # Check response structure
            if 'similar_products' in data:
                products = data['similar_products']
                print(f"   - Similar Products Found: {len(products)}")
                print("✅ Production API format is correct!")
                return True
            else:
                print("❌ Missing 'similar_products' key in production response")
                print("   Available keys:", list(data.keys()))
                return False
                
        else:
            print(f"❌ Production API returned status code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Production test failed: {e}")
        return False

def main():
    """Run visual search response format tests"""
    print("🚀 VISUAL SEARCH RESPONSE FORMAT TESTING")
    print("=" * 80)
    
    # Test results
    results = {
        'direct_api': False,
        'production_api': False
    }
    
    # Run tests
    results['direct_api'] = test_visual_search_response()
    results['production_api'] = test_production_visual_search()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 VISUAL SEARCH RESPONSE FORMAT TEST SUMMARY")
    print("=" * 80)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} - {test_name.replace('_', ' ').title()}")
    
    print(f"\n🎯 Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 VISUAL SEARCH RESPONSE FORMAT IS CORRECT!")
        print("✅ Frontend should receive proper data structure")
        print("✅ Both direct and production APIs working")
    else:
        print("❌ VISUAL SEARCH RESPONSE FORMAT ISSUES DETECTED")
        print("🚨 Frontend may not display results correctly")
    
    print("=" * 80)
    return passed_tests == total_tests

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
