#!/usr/bin/env python3
"""
Complete Database Clearing Script
=================================

This script completely clears all data from the database by:
1. Disabling foreign key checks
2. Truncating all tables
3. Re-enabling foreign key checks
4. Resetting auto-increment counters

Usage:
    python clear_database.py [--confirm]
"""

import os
import sys
import argparse

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

def clear_database_completely():
    """Completely clear all database tables"""
    print("🗑️  COMPLETE DATABASE CLEARING")
    print("=" * 50)
    
    with app.app_context():
        try:
            # Get database engine
            engine = db.engine
            
            print("📋 Getting list of all tables...")
            
            # Get all table names
            with engine.connect() as connection:
                # Disable foreign key checks
                print("🔓 Disabling foreign key checks...")
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                
                # Get all table names
                result = connection.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
                
                print(f"📊 Found {len(tables)} tables to clear")
                
                # Truncate each table
                for table in tables:
                    try:
                        print(f"  🗑️  Clearing table: {table}")
                        connection.execute(text(f"TRUNCATE TABLE `{table}`"))
                    except Exception as e:
                        print(f"  ⚠️  Warning clearing {table}: {e}")
                        # Try DELETE if TRUNCATE fails
                        try:
                            connection.execute(text(f"DELETE FROM `{table}`"))
                            print(f"  ✅ Deleted all rows from {table}")
                        except Exception as e2:
                            print(f"  ❌ Failed to clear {table}: {e2}")
                
                # Re-enable foreign key checks
                print("🔒 Re-enabling foreign key checks...")
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                
                # Commit the transaction
                connection.commit()
            
            print("✅ Database completely cleared!")
            return True
            
        except Exception as e:
            print(f"❌ Error clearing database: {e}")
            return False

def main():
    """Main function with confirmation"""
    parser = argparse.ArgumentParser(description='Completely clear the database')
    parser.add_argument('--confirm', action='store_true', 
                       help='Confirm that you want to delete ALL data')
    
    args = parser.parse_args()
    
    if not args.confirm:
        print("⚠️  WARNING: This will DELETE ALL DATA from the database!")
        print("This action cannot be undone.")
        print("")
        response = input("Are you sure you want to continue? Type 'YES' to confirm: ")
        
        if response != 'YES':
            print("❌ Operation cancelled.")
            return False
    
    print("🚀 Starting complete database clearing...")
    success = clear_database_completely()
    
    if success:
        print("\n🎉 Database successfully cleared!")
        print("\n🔄 Next steps:")
        print("1. Run: python seed_database.py --products=100")
        print("2. Run: python reindex_products.py")
        print("3. Test the application")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
