{"timestamp": "2025-07-07T19:14:21.975152", "fixes_applied": [{"category": "BACKUP", "message": "Backup preparation noted: allora_db_backup_20250707_191421.sql", "success": true, "timestamp": "2025-07-07T19:14:21.569248"}, {"category": "CONSOLIDATION", "message": "Duplicate table 'users' marked for manual review", "success": true, "timestamp": "2025-07-07T19:14:21.791898"}, {"category": "CONSOLIDATION", "message": "Duplicate table 'products' marked for manual review", "success": true, "timestamp": "2025-07-07T19:14:21.803865"}, {"category": "CONSOLIDATION", "message": "Copied 1 records from order to orders", "success": true, "timestamp": "2025-07-07T19:14:21.822420"}, {"category": "CONSOLIDATION", "message": "Duplicate table 'order' marked for manual review", "success": true, "timestamp": "2025-07-07T19:14:21.822420"}, {"category": "CONSOLIDATION", "message": "Copied 1 records from sellers to seller", "success": true, "timestamp": "2025-07-07T19:14:21.839268"}, {"category": "CONSOLIDATION", "message": "Duplicate table 'sellers' marked for manual review", "success": true, "timestamp": "2025-07-07T19:14:21.839268"}, {"category": "FOREIGN_KEY", "message": "Added FK constraint: order_item.order_id -> orders.id", "success": true, "timestamp": "2025-07-07T19:14:21.943984"}], "errors": [{"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'user': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE user ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.606828"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'users': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE users ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.611214"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'product': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE product ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.618416"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'products': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE products ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.622404"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'order': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE `order` ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.626092"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'orders': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE orders ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.633077"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'order_item': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE order_item ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.637790"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'seller': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE seller ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.643792"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'sellers': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE sellers ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.650766"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'cart_item': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE cart_item ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.655739"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'product_review': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE product_review ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.659249"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'wishlist': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE wishlist ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.666233"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'payment_transaction': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE payment_transaction ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.672218"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'shipping_carriers': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE shipping_carriers ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.687180"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'coupon': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE coupon ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.702148"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'rma_request': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE rma_request ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.718095"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'rma_item': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE rma_item ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.741857"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'search_analytics': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE search_analytics ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.751007"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'community_post': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE community_post ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.764984"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'post_comment': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE post_comment ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.768959"}, {"category": "PRIMARY_KEY", "message": "Failed to add primary key to 'admin_user': (mysql.connector.errors.ProgrammingError) 1060 (42S21): Duplicate column name 'id'\n[SQL: ALTER TABLE admin_user ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "success": false, "timestamp": "2025-07-07T19:14:21.774945"}]}