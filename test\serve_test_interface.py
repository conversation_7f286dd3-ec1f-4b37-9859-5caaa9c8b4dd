#!/usr/bin/env python3
"""
Simple HTTP Server for Elasticsearch Test Interface
==================================================

This script serves the HTML test interface on a local web server
so you can easily test the Elasticsearch functionality in your browser.
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def serve_test_interface(port=8080):
    """Serve the test interface on the specified port"""
    
    # Get the directory containing this script
    script_dir = Path(__file__).parent
    html_file = script_dir / "elasticsearch_test_interface.html"
    
    # Check if the HTML file exists
    if not html_file.exists():
        print(f"❌ Error: {html_file} not found!")
        print("Make sure the elasticsearch_test_interface.html file is in the same directory.")
        return False
    
    # Change to the script directory
    os.chdir(script_dir)
    
    # Create a custom handler that serves our HTML file as the index
    class CustomHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            if self.path == '/' or self.path == '/index.html':
                self.path = '/elasticsearch_test_interface.html'
            return super().do_GET()
        
        def end_headers(self):
            # Add CORS headers to allow cross-origin requests
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
    
    try:
        with socketserver.TCPServer(("", port), CustomHandler) as httpd:
            print("🚀 ELASTICSEARCH TEST INTERFACE SERVER")
            print("=" * 50)
            print(f"✅ Server started successfully!")
            print(f"📍 Local URL: http://localhost:{port}")
            print(f"📁 Serving from: {script_dir}")
            print(f"📄 HTML file: {html_file.name}")
            print()
            print("🔧 SETUP INSTRUCTIONS:")
            print("1. Make sure your Allora backend is running on http://localhost:5000")
            print("2. The test interface will automatically connect to your backend")
            print("3. Use the web interface to test all Elasticsearch functionality")
            print()
            print("⌨️  KEYBOARD SHORTCUTS:")
            print("• Ctrl+Enter: Run search")
            print("• Ctrl+Shift+T: Run all tests")
            print("• Ctrl+Shift+C: Clear results")
            print()
            print("🌐 Opening browser...")
            
            # Try to open the browser
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("✅ Browser opened successfully!")
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print(f"Please manually open: http://localhost:{port}")
            
            print()
            print("🛑 Press Ctrl+C to stop the server")
            print("=" * 50)
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        return True
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Error: Port {port} is already in use!")
            print(f"Try a different port: python {sys.argv[0]} --port 8081")
        else:
            print(f"❌ Error starting server: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main function with command line argument parsing"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Serve the Elasticsearch Test Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python serve_test_interface.py                    # Serve on port 8080
  python serve_test_interface.py --port 8081       # Serve on port 8081
  python serve_test_interface.py --help            # Show this help

The test interface will connect to your Allora backend at http://localhost:5000
Make sure your backend is running before using the test interface.
        """
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=8080,
        help='Port to serve the interface on (default: 8080)'
    )
    
    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='Do not automatically open the browser'
    )
    
    args = parser.parse_args()
    
    # Validate port range
    if not (1024 <= args.port <= 65535):
        print("❌ Error: Port must be between 1024 and 65535")
        return False
    
    # Serve the interface
    success = serve_test_interface(args.port)
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
