# Allora Backend API Authentication Testing Script
# ================================================

$baseUrl = "http://localhost:5000"
$testResults = @()

Write-Host "Starting Allora Backend API Authentication Testing..." -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# Test 1: User Registration
Write-Host "`n1. Testing User Registration..." -ForegroundColor Yellow
$registrationData = @{
    username = "testuser123"
    email = "<EMAIL>"
    password = "TestPass123!"
    address = "123 Test Street, Test City"
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/signup" -Method POST -Body $registrationData -ContentType "application/json"
    Write-Host "Registration Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor White
    $testResults += "Registration: PASS"
} catch {
    Write-Host "Registration Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Registration: FAIL"
}

# Test 2: User Login
Write-Host "`n2. Testing User Login..." -ForegroundColor Yellow
$loginData = @{
    email = "<EMAIL>"
    password = "TestPass123!"
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "Login Status: $($response.StatusCode)" -ForegroundColor Green
    $loginResponse = $response.Content | ConvertFrom-Json
    Write-Host "Response: $($response.Content)" -ForegroundColor White
    
    # Extract token for further tests
    $global:authToken = $loginResponse.token
    Write-Host "Auth Token: $($global:authToken.Substring(0, 20))..." -ForegroundColor Cyan
    $testResults += "Login: PASS"
} catch {
    Write-Host "Login Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Login: FAIL"
}

# Test 3: Invalid Login Attempt
Write-Host "`n3. Testing Invalid Login..." -ForegroundColor Yellow
$invalidLoginData = @{
    email = "<EMAIL>"
    password = "WrongPassword123!"
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/login" -Method POST -Body $invalidLoginData -ContentType "application/json"
    Write-Host "Invalid Login Should Have Failed but got: $($response.StatusCode)" -ForegroundColor Red
    $testResults += "Invalid Login Test: FAIL"
} catch {
    Write-Host "Invalid Login Correctly Rejected: $($_.Exception.Message)" -ForegroundColor Green
    $testResults += "Invalid Login Test: PASS"
}

# Test 4: Logout
if ($global:authToken) {
    Write-Host "`n4. Testing User Logout..." -ForegroundColor Yellow
    $headers = @{
        "Authorization" = "Bearer $global:authToken"
        "Content-Type" = "application/json"
    }
    
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/api/auth/logout" -Method POST -Headers $headers
        Write-Host "Logout Status: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "Response: $($response.Content)" -ForegroundColor White
        $testResults += "Logout: PASS"
    } catch {
        Write-Host "Logout Failed: $($_.Exception.Message)" -ForegroundColor Red
        $testResults += "Logout: FAIL"
    }
}

# Test Summary
Write-Host "`nTest Summary:" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan
foreach ($result in $testResults) {
    if ($result -like "*PASS*") {
        Write-Host "$result" -ForegroundColor Green
    } else {
        Write-Host "$result" -ForegroundColor Red
    }
}

Write-Host "`nAuthentication Flow Testing Complete!" -ForegroundColor Cyan
