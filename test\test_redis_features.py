#!/usr/bin/env python3
"""
Redis Features Testing Script
============================

This script tests all current Redis features to verify functionality
and identify integration issues.

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import sys
import json
import time
import uuid
from datetime import datetime, timedelta

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_redis_basic_connection():
    """Test basic Redis connection"""
    print("🔍 Testing Basic Redis Connection...")
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        result = r.ping()
        print(f"✅ Basic Redis Connection: {result}")
        
        # Test basic operations
        r.set('test_key', 'test_value')
        value = r.get('test_key')
        r.delete('test_key')
        print(f"✅ Basic Operations: Set/Get/Delete working")
        return True
    except Exception as e:
        print(f"❌ Basic Redis Connection Failed: {e}")
        return False

def test_redis_config_module():
    """Test redis_config.py module"""
    print("\n🔍 Testing redis_config.py Module...")
    try:
        from redis_config import test_redis_connection, get_redis_config, get_redis_cache, get_session_manager
        
        # Test connection
        result = test_redis_connection()
        print(f"✅ Redis Config Connection Test: {result['available']}")
        if result['available']:
            print(f"   Version: {result['info']['version']}")
            print(f"   Memory: {result['info']['memory_used']}")
            print(f"   Clients: {result['info']['connected_clients']}")
        
        # Test cache
        cache = get_redis_cache()
        cache.set('test_cache_key', {'test': 'data'}, expire=60)
        cached_data = cache.get('test_cache_key')
        cache.delete('test_cache_key')
        print(f"✅ Redis Cache: {cached_data}")
        
        # Test session manager
        session_mgr = get_session_manager()
        session_data = {'user_id': 123, 'username': 'test_user'}
        session_mgr.set_session('test_session', session_data, expire=3600)
        retrieved_session = session_mgr.get_session('test_session')
        session_mgr.delete_session('test_session')
        print(f"✅ Session Manager: {retrieved_session}")
        
        return True
    except Exception as e:
        print(f"❌ Redis Config Module Failed: {e}")
        return False

def test_jwt_blacklisting():
    """Test JWT token blacklisting feature"""
    print("\n🔍 Testing JWT Token Blacklisting...")
    try:
        # Import the functions from app.py
        from app import is_token_blacklisted, blacklist_token
        
        # Test with a fake JTI
        test_jti = str(uuid.uuid4())
        
        # Check if token is blacklisted (should be False)
        is_blacklisted_before = is_token_blacklisted(test_jti)
        print(f"✅ Token blacklist check (before): {is_blacklisted_before}")
        
        # Blacklist the token
        blacklist_result = blacklist_token(test_jti)
        print(f"✅ Token blacklisting: {blacklist_result}")
        
        # Check if token is now blacklisted (should be True)
        is_blacklisted_after = is_token_blacklisted(test_jti)
        print(f"✅ Token blacklist check (after): {is_blacklisted_after}")
        
        return True
    except Exception as e:
        print(f"❌ JWT Blacklisting Failed: {e}")
        return False

def test_behavior_tracking():
    """Test user behavior tracking Redis integration"""
    print("\n🔍 Testing User Behavior Tracking...")
    try:
        # Test if behavior tracker is using Redis
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        # Check for behavior tracking keys
        behavior_keys = r.keys('behavior:*')
        user_keys = r.keys('user:*')
        interaction_keys = r.keys('interaction:*')
        
        print(f"✅ Behavior tracking keys found: {len(behavior_keys)}")
        print(f"✅ User keys found: {len(user_keys)}")
        print(f"✅ Interaction keys found: {len(interaction_keys)}")
        
        # Test setting a behavior tracking entry
        test_key = f"behavior:test:{int(time.time())}"
        test_data = {
            'user_id': 123,
            'action': 'test_action',
            'timestamp': datetime.utcnow().isoformat()
        }
        r.setex(test_key, 300, json.dumps(test_data))  # 5 minute expiry
        
        # Retrieve and verify
        retrieved = r.get(test_key)
        if retrieved:
            retrieved_data = json.loads(retrieved)
            print(f"✅ Behavior tracking test: {retrieved_data['action']}")
        
        # Cleanup
        r.delete(test_key)
        return True
    except Exception as e:
        print(f"❌ Behavior Tracking Failed: {e}")
        return False

def test_recommendation_system():
    """Test recommendation system Redis integration"""
    print("\n🔍 Testing Recommendation System...")
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        # Check for recommendation keys
        rec_keys = r.keys('recommendation:*')
        ml_keys = r.keys('ml:*')
        cache_keys = r.keys('cache:*')
        
        print(f"✅ Recommendation keys found: {len(rec_keys)}")
        print(f"✅ ML cache keys found: {len(ml_keys)}")
        print(f"✅ General cache keys found: {len(cache_keys)}")
        
        # Test setting a recommendation cache entry
        test_key = f"recommendation:user:123:{int(time.time())}"
        test_recommendations = {
            'user_id': 123,
            'products': [1, 2, 3, 4, 5],
            'scores': [0.9, 0.8, 0.7, 0.6, 0.5],
            'generated_at': datetime.utcnow().isoformat()
        }
        r.setex(test_key, 3600, json.dumps(test_recommendations))  # 1 hour expiry
        
        # Retrieve and verify
        retrieved = r.get(test_key)
        if retrieved:
            retrieved_data = json.loads(retrieved)
            print(f"✅ Recommendation cache test: {len(retrieved_data['products'])} products")
        
        # Cleanup
        r.delete(test_key)
        return True
    except Exception as e:
        print(f"❌ Recommendation System Failed: {e}")
        return False

def test_health_monitoring():
    """Test health monitoring Redis integration"""
    print("\n🔍 Testing Health Monitoring...")
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        # Test Redis info (similar to health check)
        info = r.info()
        print(f"✅ Redis Version: {info.get('redis_version')}")
        print(f"✅ Connected Clients: {info.get('connected_clients')}")
        print(f"✅ Used Memory: {info.get('used_memory_human')}")
        print(f"✅ Uptime: {info.get('uptime_in_seconds')} seconds")
        
        # Test ping
        ping_result = r.ping()
        print(f"✅ Health Check Ping: {ping_result}")
        
        return True
    except Exception as e:
        print(f"❌ Health Monitoring Failed: {e}")
        return False

def analyze_redis_connections():
    """Analyze current Redis connections in the codebase"""
    print("\n🔍 Analyzing Redis Connection Patterns...")
    
    # Read app.py and count Redis connections
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count Redis connection patterns
        import re
        redis_imports = len(re.findall(r'import redis', content))
        redis_connections = len(re.findall(r'redis\.Redis\(', content))
        redis_clients = len(re.findall(r'redis_client\s*=', content))
        
        print(f"📊 Redis imports found: {redis_imports}")
        print(f"📊 Redis.Redis() connections: {redis_connections}")
        print(f"📊 redis_client assignments: {redis_clients}")
        
        # Find specific connection patterns
        jwt_connections = len(re.findall(r'redis\.Redis.*blacklist', content, re.IGNORECASE))
        behavior_connections = len(re.findall(r'redis\.Redis.*behavior', content, re.IGNORECASE))
        
        print(f"📊 JWT-related connections: {jwt_connections}")
        print(f"📊 Behavior-related connections: {behavior_connections}")
        
        return {
            'imports': redis_imports,
            'connections': redis_connections,
            'clients': redis_clients
        }
    except Exception as e:
        print(f"❌ Analysis Failed: {e}")
        return None

def main():
    """Main testing function"""
    print("🚀 Redis Features Testing Suite")
    print("=" * 50)
    
    results = {}
    
    # Test basic connection
    results['basic_connection'] = test_redis_basic_connection()
    
    # Test redis_config module
    results['redis_config_module'] = test_redis_config_module()
    
    # Test specific features
    results['jwt_blacklisting'] = test_jwt_blacklisting()
    results['behavior_tracking'] = test_behavior_tracking()
    results['recommendation_system'] = test_recommendation_system()
    results['health_monitoring'] = test_health_monitoring()
    
    # Analyze connections
    connection_analysis = analyze_redis_connections()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if connection_analysis:
        print(f"\n📊 Connection Analysis:")
        print(f"   - Multiple Redis imports: {connection_analysis['imports'] > 1}")
        print(f"   - Multiple connections: {connection_analysis['connections'] > 1}")
        print(f"   - Needs centralization: {connection_analysis['connections'] > 2}")
    
    return results

if __name__ == '__main__':
    main()
