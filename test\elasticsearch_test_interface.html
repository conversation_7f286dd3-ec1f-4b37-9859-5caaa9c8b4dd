<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elasticsearch System Test Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .results {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .results pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }

        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .product-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .product-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .product-card .price {
            color: #27ae60;
            font-weight: bold;
            font-size: 1.2em;
        }

        .product-card .category {
            background: #3498db;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            display: inline-block;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .container {
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Elasticsearch System Test Interface</h1>
            <p>Comprehensive testing interface for Allora's search system</p>
        </div>

        <div class="main-content">
            <!-- Search Testing Section -->
            <div class="section">
                <h2>🔎 Product Search</h2>
                <div class="form-group">
                    <label for="searchQuery">Search Query:</label>
                    <input type="text" id="searchQuery" placeholder="Enter search terms..." value="laptop">
                </div>
                
                <div class="filters-grid">
                    <div class="form-group">
                        <label for="sortBy">Sort By:</label>
                        <select id="sortBy">
                            <option value="relevance">Relevance</option>
                            <option value="price">Price</option>
                            <option value="rating">Rating</option>
                            <option value="popularity">Popularity</option>
                            <option value="newest">Newest</option>
                            <option value="name">Name</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="sortOrder">Sort Order:</label>
                        <select id="sortOrder">
                            <option value="desc">Descending</option>
                            <option value="asc">Ascending</option>
                        </select>
                    </div>
                </div>

                <div class="filters-grid">
                    <div class="form-group">
                        <label for="pageNum">Page:</label>
                        <input type="number" id="pageNum" value="1" min="1">
                    </div>
                    
                    <div class="form-group">
                        <label for="perPage">Per Page:</label>
                        <input type="number" id="perPage" value="10" min="1" max="50">
                    </div>
                </div>

                <button class="btn" onclick="performSearch()">🔍 Search Products</button>
                <button class="btn btn-success" onclick="getSearchFilters()">📊 Get Filters</button>
                <button class="btn btn-warning" onclick="clearSearchResults()">🗑️ Clear Results</button>
                
                <div class="loading" id="searchLoading">
                    <div class="spinner"></div>
                    <p>Searching...</p>
                </div>
                
                <div class="results" id="searchResults"></div>
            </div>

            <!-- Advanced Features Section -->
            <div class="section">
                <h2>⚡ Advanced Features</h2>
                
                <div class="form-group">
                    <label for="autocompleteQuery">Autocomplete Query:</label>
                    <input type="text" id="autocompleteQuery" placeholder="Type to get suggestions..." value="lap">
                </div>
                <button class="btn" onclick="getAutocomplete()">💡 Get Autocomplete</button>
                
                <div class="form-group" style="margin-top: 20px;">
                    <label for="similarProductId">Similar Products (Product ID):</label>
                    <input type="number" id="similarProductId" placeholder="Enter product ID..." value="1">
                </div>
                <button class="btn" onclick="getSimilarProducts()">🔗 Find Similar</button>
                
                <div class="form-group" style="margin-top: 20px;">
                    <label for="complexQuery">Complex Boolean Query:</label>
                    <textarea id="complexQuery" rows="3" placeholder='[{"field": "name", "query": "laptop", "operator": "must"}]'>[{"field": "name", "query": "laptop", "operator": "must"}]</textarea>
                </div>
                <button class="btn btn-warning" onclick="performComplexSearch()">🧠 Complex Search</button>
                
                <div class="results" id="advancedResults"></div>
            </div>

            <!-- System Health Section -->
            <div class="section">
                <h2>🏥 System Health</h2>
                
                <button class="btn btn-success" onclick="checkSearchHealth()">❤️ Check Health</button>
                <button class="btn btn-warning" onclick="getSearchSuggestions()">💭 Get Suggestions</button>
                <button class="btn" onclick="testElasticsearchConnection()">🔌 Test Connection</button>
                
                <div class="results" id="healthResults"></div>
            </div>

            <!-- Analytics Section -->
            <div class="section">
                <h2>📈 Search Analytics</h2>
                
                <div class="form-group">
                    <label for="analyticsQuery">Track Search Query:</label>
                    <input type="text" id="analyticsQuery" placeholder="Query to track..." value="test search">
                </div>
                
                <div class="form-group">
                    <label for="userId">User ID (optional):</label>
                    <input type="text" id="userId" placeholder="User ID..." value="test-user-123">
                </div>
                
                <button class="btn" onclick="trackSearchAnalytics()">📊 Track Search</button>
                <button class="btn btn-success" onclick="getSearchPerformance()">⚡ Get Performance</button>
                
                <div class="results" id="analyticsResults"></div>
            </div>
        </div>

        <!-- Full Width Results Section -->
        <div class="main-content">
            <div class="section full-width">
                <h2>📋 Complete Test Results</h2>
                <div id="statusDisplay" class="status info">Ready to test Elasticsearch functionality</div>
                <div class="results" id="completeResults" style="max-height: 500px;"></div>
            </div>
        </div>
    </div>

    <script>
        // Base API URL - adjust this to match your backend
        const API_BASE = 'http://localhost:5000';
        
        // Utility function to show loading
        function showLoading(elementId) {
            document.getElementById(elementId).style.display = 'block';
        }
        
        // Utility function to hide loading
        function hideLoading(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }
        
        // Utility function to display results
        function displayResults(elementId, data, title = '') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const resultHtml = `
                <div style="border-bottom: 1px solid #ddd; margin-bottom: 15px; padding-bottom: 10px;">
                    <strong>${title} - ${timestamp}</strong>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            element.innerHTML = resultHtml + element.innerHTML;
        }
        
        // Update status display
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('statusDisplay');
            statusElement.className = `status ${type}`;
            statusElement.textContent = message;
        }
        
        // Main search function
        async function performSearch() {
            const query = document.getElementById('searchQuery').value;
            const sortBy = document.getElementById('sortBy').value;
            const sortOrder = document.getElementById('sortOrder').value;
            const page = document.getElementById('pageNum').value;
            const perPage = document.getElementById('perPage').value;
            
            showLoading('searchLoading');
            updateStatus('Performing product search...', 'info');
            
            try {
                const params = new URLSearchParams({
                    q: query,
                    sort_by: sortBy,
                    sort_order: sortOrder,
                    page: page,
                    per_page: perPage,
                    include_aggregations: 'true'
                });
                
                const response = await fetch(`${API_BASE}/api/search?${params}`);
                const data = await response.json();
                
                hideLoading('searchLoading');
                
                if (data.success) {
                    updateStatus(`Search completed: ${data.data.total} results found`, 'success');
                    displaySearchResults(data.data);
                } else {
                    updateStatus(`Search failed: ${data.message}`, 'error');
                    displayResults('searchResults', data, 'Search Error');
                }
                
                displayResults('completeResults', data, 'Product Search');
                
            } catch (error) {
                hideLoading('searchLoading');
                updateStatus(`Search error: ${error.message}`, 'error');
                displayResults('searchResults', {error: error.message}, 'Search Error');
            }
        }
        
        // Display search results in a user-friendly format
        function displaySearchResults(data) {
            const resultsElement = document.getElementById('searchResults');
            
            if (data.products && data.products.length > 0) {
                let html = `<h4>Found ${data.total} products (Page ${data.page} of ${data.total_pages})</h4>`;
                
                data.products.forEach(product => {
                    html += `
                        <div class="product-card">
                            <h4>${product.name || 'Unnamed Product'}</h4>
                            <p>${product.description || 'No description available'}</p>
                            <div class="price">$${product.price || 'N/A'}</div>
                            <span class="category">${product.category || 'Uncategorized'}</span>
                            ${product.brand ? `<span class="category">${product.brand}</span>` : ''}
                        </div>
                    `;
                });
                
                resultsElement.innerHTML = html;
            } else {
                resultsElement.innerHTML = '<p>No products found matching your search criteria.</p>';
            }
        }

        // Get search filters
        async function getSearchFilters() {
            updateStatus('Getting search filters...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/search/filters`);
                const data = await response.json();

                if (data.success) {
                    updateStatus('Search filters retrieved successfully', 'success');
                } else {
                    updateStatus(`Failed to get filters: ${data.message}`, 'error');
                }

                displayResults('searchResults', data, 'Search Filters');
                displayResults('completeResults', data, 'Search Filters');

            } catch (error) {
                updateStatus(`Filter error: ${error.message}`, 'error');
                displayResults('searchResults', {error: error.message}, 'Filter Error');
            }
        }

        // Get autocomplete suggestions
        async function getAutocomplete() {
            const query = document.getElementById('autocompleteQuery').value;
            updateStatus('Getting autocomplete suggestions...', 'info');

            try {
                const params = new URLSearchParams({ q: query, limit: 10 });
                const response = await fetch(`${API_BASE}/api/search/autocomplete?${params}`);
                const data = await response.json();

                if (data.success) {
                    updateStatus(`Autocomplete completed: ${data.data.suggestions.length} suggestions`, 'success');
                } else {
                    updateStatus(`Autocomplete failed: ${data.message}`, 'error');
                }

                displayResults('advancedResults', data, 'Autocomplete');
                displayResults('completeResults', data, 'Autocomplete');

            } catch (error) {
                updateStatus(`Autocomplete error: ${error.message}`, 'error');
                displayResults('advancedResults', {error: error.message}, 'Autocomplete Error');
            }
        }

        // Get similar products
        async function getSimilarProducts() {
            const productId = document.getElementById('similarProductId').value;
            updateStatus('Finding similar products...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/search/similar/${productId}`);
                const data = await response.json();

                if (data.success) {
                    updateStatus(`Similar products found: ${data.data.products.length} products`, 'success');
                } else {
                    updateStatus(`Similar products failed: ${data.message}`, 'error');
                }

                displayResults('advancedResults', data, 'Similar Products');
                displayResults('completeResults', data, 'Similar Products');

            } catch (error) {
                updateStatus(`Similar products error: ${error.message}`, 'error');
                displayResults('advancedResults', {error: error.message}, 'Similar Products Error');
            }
        }

        // Perform complex boolean search
        async function performComplexSearch() {
            const queryText = document.getElementById('complexQuery').value;
            updateStatus('Performing complex search...', 'info');

            try {
                let searchTerms;
                try {
                    searchTerms = JSON.parse(queryText);
                } catch (parseError) {
                    throw new Error('Invalid JSON in complex query');
                }

                const response = await fetch(`${API_BASE}/api/search/complex`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        search_terms: searchTerms,
                        page: 1,
                        per_page: 10
                    })
                });

                const data = await response.json();

                if (data.success) {
                    updateStatus(`Complex search completed: ${data.data.total} results`, 'success');
                } else {
                    updateStatus(`Complex search failed: ${data.message}`, 'error');
                }

                displayResults('advancedResults', data, 'Complex Search');
                displayResults('completeResults', data, 'Complex Search');

            } catch (error) {
                updateStatus(`Complex search error: ${error.message}`, 'error');
                displayResults('advancedResults', {error: error.message}, 'Complex Search Error');
            }
        }

        // Check search system health
        async function checkSearchHealth() {
            updateStatus('Checking search system health...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/search/health`);
                const data = await response.json();

                if (data.success) {
                    const isHealthy = data.data.elasticsearch_connected;
                    updateStatus(`Health check completed: ${isHealthy ? 'Healthy' : 'Issues detected'}`,
                               isHealthy ? 'success' : 'error');
                } else {
                    updateStatus(`Health check failed: ${data.message}`, 'error');
                }

                displayResults('healthResults', data, 'System Health');
                displayResults('completeResults', data, 'System Health');

            } catch (error) {
                updateStatus(`Health check error: ${error.message}`, 'error');
                displayResults('healthResults', {error: error.message}, 'Health Check Error');
            }
        }

        // Get search suggestions
        async function getSearchSuggestions() {
            updateStatus('Getting search suggestions...', 'info');

            try {
                const params = new URLSearchParams({ q: 'lap', limit: 10 });
                const response = await fetch(`${API_BASE}/api/search/suggestions?${params}`);
                const data = await response.json();

                if (data.success) {
                    updateStatus(`Search suggestions retrieved: ${data.data.suggestions.length} suggestions`, 'success');
                } else {
                    updateStatus(`Search suggestions failed: ${data.message}`, 'error');
                }

                displayResults('healthResults', data, 'Search Suggestions');
                displayResults('completeResults', data, 'Search Suggestions');

            } catch (error) {
                updateStatus(`Search suggestions error: ${error.message}`, 'error');
                displayResults('healthResults', {error: error.message}, 'Search Suggestions Error');
            }
        }

        // Test Elasticsearch connection
        async function testElasticsearchConnection() {
            updateStatus('Testing Elasticsearch connection...', 'info');

            try {
                // Use the health endpoint to test connection
                const response = await fetch(`${API_BASE}/api/search/health`);
                const data = await response.json();

                if (response.ok && data.success) {
                    const connected = data.data.elasticsearch_connected;
                    updateStatus(`Connection test: ${connected ? 'Connected' : 'Disconnected'}`,
                               connected ? 'success' : 'error');
                } else {
                    updateStatus('Connection test failed', 'error');
                }

                displayResults('healthResults', data, 'Connection Test');
                displayResults('completeResults', data, 'Connection Test');

            } catch (error) {
                updateStatus(`Connection test error: ${error.message}`, 'error');
                displayResults('healthResults', {error: error.message}, 'Connection Test Error');
            }
        }

        // Track search analytics
        async function trackSearchAnalytics() {
            const query = document.getElementById('analyticsQuery').value;
            const userId = document.getElementById('userId').value;
            updateStatus('Tracking search analytics...', 'info');

            try {
                const analyticsData = {
                    query: query,
                    user_id: userId || null,
                    session_id: 'test-session-' + Date.now(),
                    search_type: 'simple',
                    results_count: Math.floor(Math.random() * 100),
                    response_time_ms: Math.floor(Math.random() * 500) + 50,
                    elasticsearch_time_ms: Math.floor(Math.random() * 200) + 10,
                    clicked_results: [],
                    conversion_events: []
                };

                const response = await fetch(`${API_BASE}/api/search/analytics`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(analyticsData)
                });

                const data = await response.json();

                if (data.success) {
                    updateStatus('Search analytics tracked successfully', 'success');
                } else {
                    updateStatus(`Analytics tracking failed: ${data.message}`, 'error');
                }

                displayResults('analyticsResults', data, 'Track Analytics');
                displayResults('completeResults', data, 'Track Analytics');

            } catch (error) {
                updateStatus(`Analytics tracking error: ${error.message}`, 'error');
                displayResults('analyticsResults', {error: error.message}, 'Analytics Error');
            }
        }

        // Get search performance metrics
        async function getSearchPerformance() {
            updateStatus('Getting search performance metrics...', 'info');

            try {
                const params = new URLSearchParams({ time_range: '24h' });
                const response = await fetch(`${API_BASE}/api/search/analytics/performance?${params}`);
                const data = await response.json();

                if (data.success) {
                    updateStatus('Performance metrics retrieved successfully', 'success');
                } else {
                    updateStatus(`Performance metrics failed: ${data.message}`, 'error');
                }

                displayResults('analyticsResults', data, 'Performance Metrics');
                displayResults('completeResults', data, 'Performance Metrics');

            } catch (error) {
                updateStatus(`Performance metrics error: ${error.message}`, 'error');
                displayResults('analyticsResults', {error: error.message}, 'Performance Error');
            }
        }

        // Run comprehensive test suite
        async function runComprehensiveTest() {
            updateStatus('Running comprehensive test suite...', 'info');
            document.getElementById('completeResults').innerHTML = '<h3>🚀 Starting Comprehensive Test Suite</h3>';

            const tests = [
                { name: 'Basic Search', func: performSearch },
                { name: 'Search Filters', func: getSearchFilters },
                { name: 'Autocomplete', func: getAutocomplete },
                { name: 'Similar Products', func: getSimilarProducts },
                { name: 'System Health', func: checkSearchHealth },
                { name: 'Search Suggestions', func: getSearchSuggestions },
                { name: 'Connection Test', func: testElasticsearchConnection },
                { name: 'Analytics Tracking', func: trackSearchAnalytics },
                { name: 'Performance Metrics', func: getSearchPerformance }
            ];

            let passed = 0;
            let total = tests.length;

            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                updateStatus(`Running test ${i + 1}/${total}: ${test.name}`, 'info');

                try {
                    await test.func();
                    passed++;
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
                } catch (error) {
                    console.error(`Test ${test.name} failed:`, error);
                }
            }

            const successRate = ((passed / total) * 100).toFixed(1);
            updateStatus(`Comprehensive test completed: ${passed}/${total} tests passed (${successRate}%)`,
                       passed === total ? 'success' : 'error');

            displayResults('completeResults', {
                test_summary: {
                    total_tests: total,
                    passed_tests: passed,
                    success_rate: successRate + '%',
                    timestamp: new Date().toISOString()
                }
            }, 'Test Suite Summary');
        }

        // Initialize the interface
        function initializeInterface() {
            updateStatus('Elasticsearch Test Interface Ready', 'success');

            // Add event listeners for Enter key on input fields
            document.getElementById('searchQuery').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') performSearch();
            });

            document.getElementById('autocompleteQuery').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') getAutocomplete();
            });

            document.getElementById('analyticsQuery').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') trackSearchAnalytics();
            });

            // Auto-update autocomplete as user types
            document.getElementById('autocompleteQuery').addEventListener('input', function(e) {
                if (e.target.value.length >= 2) {
                    setTimeout(() => getAutocomplete(), 500);
                }
            });
        }

        // Load interface when page loads
        document.addEventListener('DOMContentLoaded', initializeInterface);

        // Add comprehensive test button functionality
        function addComprehensiveTestButton() {
            const headerElement = document.querySelector('.header');
            const testButton = document.createElement('button');
            testButton.className = 'btn btn-success';
            testButton.style.marginTop = '20px';
            testButton.innerHTML = '🧪 Run All Tests';
            testButton.onclick = runComprehensiveTest;
            headerElement.appendChild(testButton);
        }

        // Add the comprehensive test button when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(addComprehensiveTestButton, 100);
        });

        // Clear search results
        function clearSearchResults() {
            document.getElementById('searchResults').innerHTML = '';
            document.getElementById('advancedResults').innerHTML = '';
            document.getElementById('healthResults').innerHTML = '';
            document.getElementById('analyticsResults').innerHTML = '';
            document.getElementById('completeResults').innerHTML = '';
            updateStatus('Results cleared', 'info');
        }

        // Export results to JSON
        function exportResults() {
            const results = document.getElementById('completeResults').innerText;
            const blob = new Blob([results], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `elasticsearch_test_results_${new Date().toISOString().slice(0,19)}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            updateStatus('Results exported successfully', 'success');
        }

        // Test with sample data
        function loadSampleData() {
            document.getElementById('searchQuery').value = 'laptop computer';
            document.getElementById('autocompleteQuery').value = 'lap';
            document.getElementById('similarProductId').value = '1';
            document.getElementById('complexQuery').value = JSON.stringify([
                {"field": "name", "query": "laptop", "operator": "must"},
                {"field": "category", "query": "electronics", "operator": "should"}
            ], null, 2);
            document.getElementById('analyticsQuery').value = 'sample search query';
            document.getElementById('userId').value = 'test-user-' + Date.now();
            updateStatus('Sample data loaded', 'success');
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+Enter to run search
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                performSearch();
            }

            // Ctrl+Shift+T to run all tests
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                runComprehensiveTest();
            }

            // Ctrl+Shift+C to clear results
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                clearSearchResults();
            }
        });

        // Add utility buttons to header
        function addUtilityButtons() {
            const headerElement = document.querySelector('.header');
            const buttonContainer = document.createElement('div');
            buttonContainer.style.marginTop = '20px';

            const buttons = [
                { text: '🧪 Run All Tests', onclick: 'runComprehensiveTest()', class: 'btn-success' },
                { text: '📝 Load Sample Data', onclick: 'loadSampleData()', class: 'btn-warning' },
                { text: '💾 Export Results', onclick: 'exportResults()', class: 'btn' },
                { text: '🗑️ Clear All', onclick: 'clearSearchResults()', class: 'btn-danger' }
            ];

            buttons.forEach(btn => {
                const button = document.createElement('button');
                button.className = `btn ${btn.class}`;
                button.innerHTML = btn.text;
                button.setAttribute('onclick', btn.onclick);
                button.style.marginRight = '10px';
                buttonContainer.appendChild(button);
            });

            headerElement.appendChild(buttonContainer);

            // Add keyboard shortcuts info
            const shortcutsInfo = document.createElement('div');
            shortcutsInfo.style.marginTop = '15px';
            shortcutsInfo.style.fontSize = '0.9em';
            shortcutsInfo.style.opacity = '0.8';
            shortcutsInfo.innerHTML = `
                <strong>Keyboard Shortcuts:</strong>
                Ctrl+Enter (Search) | Ctrl+Shift+T (Run All Tests) | Ctrl+Shift+C (Clear Results)
            `;
            headerElement.appendChild(shortcutsInfo);
        }

        // Enhanced initialization
        function enhancedInitialization() {
            initializeInterface();
            setTimeout(() => {
                addComprehensiveTestButton();
                addUtilityButtons();
            }, 200);

            // Show initial help message
            displayResults('completeResults', {
                welcome_message: "Welcome to the Elasticsearch Test Interface!",
                instructions: [
                    "1. Use the search section to test basic product search functionality",
                    "2. Try advanced features like autocomplete and similar products",
                    "3. Check system health and performance metrics",
                    "4. Track search analytics and monitor performance",
                    "5. Use 'Run All Tests' to execute a comprehensive test suite",
                    "6. Use keyboard shortcuts for faster testing"
                ],
                api_base_url: API_BASE,
                timestamp: new Date().toISOString()
            }, 'Welcome Guide');
        }

        // Replace the original DOMContentLoaded listener
        document.addEventListener('DOMContentLoaded', enhancedInitialization);
    </script>
</body>
</html>
