#!/usr/bin/env python3
"""
Migration script to update the order table for guest checkout and enhanced features.
"""

import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

def column_exists_in_table(table_name, column_name):
    """Check if a column exists in a table"""
    result = db.session.execute(text("""
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = :table_name
        AND COLUMN_NAME = :column_name
    """), {"table_name": table_name, "column_name": column_name})
    return result.fetchone()[0] > 0

def run_order_migration():
    """Run the order table migration within Flask app context"""
    with app.app_context():
        print("Starting order table migration...")
        print("=" * 50)

        try:
            columns_to_add = [
                ("is_guest_order", "BOOLEAN DEFAULT FALSE"),
                ("guest_email", "VARCHAR(120) NULL"),
                ("guest_phone", "VARCHAR(20) NULL"),
                ("guest_session_id", "VARCHAR(100) NULL"),
                ("order_number", "VARCHAR(50) NULL"),
                ("subtotal", "DECIMAL(10,2) DEFAULT 0.00"),
                ("tax_amount", "DECIMAL(10,2) DEFAULT 0.00"),
                ("shipping_amount", "DECIMAL(10,2) DEFAULT 0.00"),
                ("discount_amount", "DECIMAL(10,2) DEFAULT 0.00"),
                ("shipping_address", "TEXT NULL"),
                ("billing_address", "TEXT NULL"),
                ("payment_method", "VARCHAR(50) NULL"),
                ("payment_status", "VARCHAR(20) DEFAULT 'pending'"),
                ("payment_reference", "VARCHAR(100) NULL"),
                ("tracking_number", "VARCHAR(100) NULL"),
                ("estimated_delivery", "DATETIME NULL"),
                ("updated_at", "DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
                ("shipped_at", "DATETIME NULL"),
                ("delivered_at", "DATETIME NULL"),
                ("order_notes", "TEXT NULL")
            ]

            print("Checking and adding missing columns...")
            for column_name, column_definition in columns_to_add:
                if not column_exists_in_table('order', column_name):
                    print(f"Adding column: {column_name}")
                    db.session.execute(text(f"""
                        ALTER TABLE `order`
                        ADD COLUMN {column_name} {column_definition}
                    """))
                else:
                    print(f"✓ Column {column_name} already exists")

            # Modify user_id to allow NULL for guest orders
            print("Modifying user_id to allow NULL...")
            db.session.execute(text("""
                ALTER TABLE `order`
                MODIFY COLUMN user_id INT NULL
            """))

            print("✓ Order table columns updated successfully")
            
            # Create all tables defined in models (for any new tables)
            print("Creating/updating all database tables...")
            db.create_all()
            print("✓ All tables created/updated successfully")
            
            # Commit changes
            db.session.commit()
            
            print("=" * 50)
            print("✅ Order table migration completed successfully!")
            print("The order table now supports:")
            print("- Guest checkout functionality")
            print("- Enhanced order tracking")
            print("- Payment processing")
            print("- Shipping and billing addresses")
            print("- Tax and discount calculations")
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            db.session.rollback()
            raise
        finally:
            db.session.close()

if __name__ == "__main__":
    run_order_migration()
