#!/usr/bin/env python3
"""
Comprehensive Database Repair Script for Allora E-commerce
Fixes critical database structure issues identified in the analysis
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
import logging
from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseRepairer:
    def __init__(self):
        self.fixes_applied = []
        self.errors = []
        
    def log_fix(self, category, message, success=True):
        fix = {
            'category': category,
            'message': message,
            'success': success,
            'timestamp': datetime.now().isoformat()
        }
        
        if success:
            self.fixes_applied.append(fix)
            print(f"✅ FIXED: [{category}] {message}")
        else:
            self.errors.append(fix)
            print(f"❌ ERROR: [{category}] {message}")

    def backup_database(self):
        """Create a backup before making changes"""
        print("\n" + "="*80)
        print("💾 CREATING DATABASE BACKUP")
        print("="*80)
        
        try:
            backup_filename = f"allora_db_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            
            # Note: In production, you would use mysqldump here
            # For now, we'll just log that a backup should be created
            print(f"📋 Backup should be created: {backup_filename}")
            print("⚠️  In production, run: mysqldump -u username -p allora_db > backup.sql")
            
            self.log_fix("BACKUP", f"Backup preparation noted: {backup_filename}")
            return True
            
        except Exception as e:
            self.log_fix("BACKUP", f"Backup failed: {e}", False)
            return False

    def fix_primary_keys(self):
        """Add primary keys to tables that are missing them"""
        print("\n" + "="*80)
        print("🔑 FIXING PRIMARY KEYS")
        print("="*80)
        
        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            # Define primary key fixes for critical tables
            pk_fixes = {
                'user': 'ALTER TABLE user ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'users': 'ALTER TABLE users ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'product': 'ALTER TABLE product ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'products': 'ALTER TABLE products ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'order': 'ALTER TABLE `order` ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'orders': 'ALTER TABLE orders ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'order_item': 'ALTER TABLE order_item ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'seller': 'ALTER TABLE seller ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'sellers': 'ALTER TABLE sellers ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'cart_item': 'ALTER TABLE cart_item ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'product_review': 'ALTER TABLE product_review ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'wishlist': 'ALTER TABLE wishlist ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'payment_transaction': 'ALTER TABLE payment_transaction ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'shipping_carriers': 'ALTER TABLE shipping_carriers ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'coupon': 'ALTER TABLE coupon ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'rma_request': 'ALTER TABLE rma_request ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'rma_item': 'ALTER TABLE rma_item ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'search_analytics': 'ALTER TABLE search_analytics ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'community_post': 'ALTER TABLE community_post ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'post_comment': 'ALTER TABLE post_comment ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST',
                'admin_user': 'ALTER TABLE admin_user ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST'
            }
            
            for table_name, alter_query in pk_fixes.items():
                if table_name in tables:
                    try:
                        # Check if table already has primary key
                        columns = inspector.get_columns(table_name)
                        has_pk = any(col.get('primary_key', False) for col in columns)
                        
                        if not has_pk:
                            db.session.execute(text(alter_query))
                            db.session.commit()
                            self.log_fix("PRIMARY_KEY", f"Added primary key to table '{table_name}'")
                        else:
                            print(f"⏭️  Table '{table_name}' already has primary key")
                            
                    except Exception as e:
                        self.log_fix("PRIMARY_KEY", f"Failed to add primary key to '{table_name}': {e}", False)
                        db.session.rollback()

    def consolidate_duplicate_tables(self):
        """Consolidate duplicate table structures"""
        print("\n" + "="*80)
        print("🔄 CONSOLIDATING DUPLICATE TABLES")
        print("="*80)
        
        with app.app_context():
            # Define table consolidation strategy
            consolidations = [
                {
                    'primary': 'user',
                    'duplicate': 'users',
                    'strategy': 'merge_data'
                },
                {
                    'primary': 'product', 
                    'duplicate': 'products',
                    'strategy': 'merge_data'
                },
                {
                    'primary': 'orders',
                    'duplicate': 'order', 
                    'strategy': 'merge_data'
                },
                {
                    'primary': 'seller',
                    'duplicate': 'sellers',
                    'strategy': 'merge_data'
                }
            ]
            
            for consolidation in consolidations:
                try:
                    primary_table = consolidation['primary']
                    duplicate_table = consolidation['duplicate']
                    
                    # Check if both tables exist
                    inspector = inspect(db.engine)
                    tables = inspector.get_table_names()
                    
                    if primary_table in tables and duplicate_table in tables:
                        # Get record counts
                        primary_count = db.session.execute(text(f"SELECT COUNT(*) FROM `{primary_table}`")).scalar()
                        duplicate_count = db.session.execute(text(f"SELECT COUNT(*) FROM `{duplicate_table}`")).scalar()
                        
                        print(f"📊 {primary_table}: {primary_count} records, {duplicate_table}: {duplicate_count} records")
                        
                        # If duplicate has data and primary doesn't, swap them
                        if duplicate_count > 0 and primary_count == 0:
                            # Copy data from duplicate to primary
                            try:
                                # Get column structures
                                primary_cols = inspector.get_columns(primary_table)
                                duplicate_cols = inspector.get_columns(duplicate_table)
                                
                                # Find common columns
                                primary_col_names = [col['name'] for col in primary_cols]
                                duplicate_col_names = [col['name'] for col in duplicate_cols]
                                common_cols = list(set(primary_col_names) & set(duplicate_col_names))
                                
                                if common_cols:
                                    cols_str = ', '.join([f"`{col}`" for col in common_cols])
                                    copy_query = f"INSERT INTO `{primary_table}` ({cols_str}) SELECT {cols_str} FROM `{duplicate_table}`"
                                    
                                    db.session.execute(text(copy_query))
                                    db.session.commit()
                                    
                                    self.log_fix("CONSOLIDATION", f"Copied {duplicate_count} records from {duplicate_table} to {primary_table}")
                                
                            except Exception as e:
                                self.log_fix("CONSOLIDATION", f"Failed to copy data from {duplicate_table} to {primary_table}: {e}", False)
                                db.session.rollback()
                        
                        # Note: We won't drop the duplicate tables automatically for safety
                        # They should be manually reviewed and dropped after verification
                        self.log_fix("CONSOLIDATION", f"Duplicate table '{duplicate_table}' marked for manual review")
                        
                except Exception as e:
                    self.log_fix("CONSOLIDATION", f"Error consolidating {consolidation}: {e}", False)

    def fix_foreign_key_constraints(self):
        """Add missing foreign key constraints"""
        print("\n" + "="*80)
        print("🔗 FIXING FOREIGN KEY CONSTRAINTS")
        print("="*80)
        
        with app.app_context():
            # Define critical foreign key constraints to add
            fk_constraints = [
                {
                    'table': 'order_item',
                    'column': 'order_id',
                    'ref_table': 'orders',
                    'ref_column': 'id',
                    'constraint_name': 'fk_order_item_order'
                },
                {
                    'table': 'order_item', 
                    'column': 'product_id',
                    'ref_table': 'product',
                    'ref_column': 'id',
                    'constraint_name': 'fk_order_item_product'
                },
                {
                    'table': 'cart_item',
                    'column': 'user_id', 
                    'ref_table': 'user',
                    'ref_column': 'id',
                    'constraint_name': 'fk_cart_item_user'
                },
                {
                    'table': 'cart_item',
                    'column': 'product_id',
                    'ref_table': 'product', 
                    'ref_column': 'id',
                    'constraint_name': 'fk_cart_item_product'
                },
                {
                    'table': 'product_review',
                    'column': 'user_id',
                    'ref_table': 'user',
                    'ref_column': 'id', 
                    'constraint_name': 'fk_product_review_user'
                },
                {
                    'table': 'product_review',
                    'column': 'product_id',
                    'ref_table': 'product',
                    'ref_column': 'id',
                    'constraint_name': 'fk_product_review_product'
                }
            ]
            
            for fk in fk_constraints:
                try:
                    # Check if constraint already exists
                    check_query = f"""
                        SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE 
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = '{fk['table']}'
                        AND COLUMN_NAME = '{fk['column']}'
                        AND REFERENCED_TABLE_NAME = '{fk['ref_table']}'
                    """
                    
                    result = db.session.execute(text(check_query))
                    if result.scalar() == 0:
                        # Add the foreign key constraint
                        alter_query = f"""
                            ALTER TABLE `{fk['table']}` 
                            ADD CONSTRAINT `{fk['constraint_name']}` 
                            FOREIGN KEY (`{fk['column']}`) 
                            REFERENCES `{fk['ref_table']}`(`{fk['ref_column']}`)
                            ON DELETE CASCADE ON UPDATE CASCADE
                        """
                        
                        db.session.execute(text(alter_query))
                        db.session.commit()
                        
                        self.log_fix("FOREIGN_KEY", f"Added FK constraint: {fk['table']}.{fk['column']} -> {fk['ref_table']}.{fk['ref_column']}")
                    else:
                        print(f"⏭️  FK constraint already exists: {fk['table']}.{fk['column']} -> {fk['ref_table']}")
                        
                except Exception as e:
                    self.log_fix("FOREIGN_KEY", f"Failed to add FK constraint {fk['constraint_name']}: {e}", False)
                    db.session.rollback()

    def clean_orphaned_data(self):
        """Clean up orphaned data"""
        print("\n" + "="*80)
        print("🧹 CLEANING ORPHANED DATA")
        print("="*80)
        
        with app.app_context():
            try:
                # Clean orphaned order items
                orphaned_query = """
                    DELETE oi FROM order_item oi 
                    LEFT JOIN orders o ON oi.order_id = o.id 
                    WHERE o.id IS NULL AND oi.order_id IS NOT NULL
                """
                
                result = db.session.execute(text(orphaned_query))
                deleted_count = result.rowcount
                db.session.commit()
                
                if deleted_count > 0:
                    self.log_fix("DATA_CLEANUP", f"Removed {deleted_count} orphaned order items")
                else:
                    print("✅ No orphaned order items found")
                    
            except Exception as e:
                self.log_fix("DATA_CLEANUP", f"Error cleaning orphaned data: {e}", False)
                db.session.rollback()

    def generate_repair_report(self):
        """Generate repair report"""
        print("\n" + "="*80)
        print("📋 DATABASE REPAIR REPORT")
        print("="*80)
        
        print(f"\n✅ FIXES APPLIED: {len(self.fixes_applied)}")
        for fix in self.fixes_applied:
            print(f"  - [{fix['category']}] {fix['message']}")
        
        print(f"\n❌ ERRORS ENCOUNTERED: {len(self.errors)}")
        for error in self.errors:
            print(f"  - [{error['category']}] {error['message']}")
        
        # Save detailed report
        report = {
            'timestamp': datetime.now().isoformat(),
            'fixes_applied': self.fixes_applied,
            'errors': self.errors
        }
        
        with open('database_repair_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Detailed repair report saved to: database_repair_report.json")

def main():
    print("🔧 Starting Comprehensive Database Repair...")
    print("⚠️  WARNING: This will modify your database structure!")
    
    # Confirm before proceeding
    response = input("\nDo you want to proceed with database repairs? (yes/no): ")
    if response.lower() != 'yes':
        print("❌ Database repair cancelled by user")
        return
    
    repairer = DatabaseRepairer()
    
    # Run repair operations
    if repairer.backup_database():
        repairer.fix_primary_keys()
        repairer.consolidate_duplicate_tables()
        repairer.fix_foreign_key_constraints()
        repairer.clean_orphaned_data()
    
    # Generate final report
    repairer.generate_repair_report()
    
    print("\n✅ Database repair completed!")
    print("🔍 Run the analysis script again to verify fixes")

if __name__ == '__main__':
    main()
