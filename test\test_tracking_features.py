"""
Test Tracking System Features
============================

Test specific tracking system features and functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_tracking_status_mapping():
    """Test carrier status mapping functionality"""
    print("\n🧪 TESTING STATUS MAPPING")
    print("-" * 40)
    
    try:
        from tracking_system import CarrierStatusMapper, TrackingStatus
        
        mapper = CarrierStatusMapper()
        
        # Test Blue Dart mappings
        test_cases = [
            ('blue_dart', 'Delivered', TrackingStatus.DELIVERED),
            ('blue_dart', 'In Transit', TrackingStatus.IN_TRANSIT),
            ('blue_dart', 'Out for Delivery', TrackingStatus.OUT_FOR_DELIVERY),
            ('delhivery', 'Delivered', TrackingStatus.DELIVERED),
            ('fedex', 'Delivered', TrackingStatus.DELIVERED),
        ]
        
        for carrier, original_status, expected in test_cases:
            mapped = mapper.map_status(carrier, original_status)
            status = "✅" if mapped == expected else "❌"
            print(f"{status} {carrier}: '{original_status}' -> {mapped}")
        
        return True
        
    except Exception as e:
        print(f"❌ Status mapping test failed: {e}")
        return False

def test_tracking_enums():
    """Test tracking enums and constants"""
    print("\n🧪 TESTING TRACKING ENUMS")
    print("-" * 40)

    try:
        from tracking_system import TrackingStatus, TrackingEventType

        # Test TrackingStatus enum - use available values
        try:
            statuses = [
                TrackingStatus.LABEL_CREATED,
                TrackingStatus.PICKUP_SCHEDULED,
                TrackingStatus.PICKED_UP,
                TrackingStatus.IN_TRANSIT,
                TrackingStatus.OUT_FOR_DELIVERY,
                TrackingStatus.DELIVERED,
                TrackingStatus.DELIVERY_ATTEMPTED
            ]

            # Try to add EXCEPTION if it exists
            if hasattr(TrackingStatus, 'EXCEPTION'):
                statuses.append(TrackingStatus.EXCEPTION)

            print("✅ TrackingStatus enum values:")
            for status in statuses:
                print(f"   - {status.name}: {status.value}")
        except AttributeError as e:
            print(f"⚠️ Some TrackingStatus values not available: {e}")

        # Test TrackingEventType enum
        try:
            event_types = [
                TrackingEventType.STATUS_UPDATE,
                TrackingEventType.LOCATION_UPDATE,
                TrackingEventType.DELIVERY_ATTEMPT,
                TrackingEventType.EXCEPTION_OCCURRED,
                TrackingEventType.ESTIMATED_DELIVERY_UPDATE
            ]

            print("✅ TrackingEventType enum values:")
            for event_type in event_types:
                print(f"   - {event_type.name}: {event_type.value}")
        except AttributeError as e:
            print(f"⚠️ Some TrackingEventType values not available: {e}")

        return True

    except Exception as e:
        print(f"❌ Enum test failed: {e}")
        return False

def test_dashboard_filters():
    """Test dashboard filter functionality"""
    print("\n🧪 TESTING DASHBOARD FILTERS")
    print("-" * 40)
    
    try:
        from tracking_dashboard import DashboardFilter
        
        filters = [
            DashboardFilter.ALL,
            DashboardFilter.ACTIVE,
            DashboardFilter.DELIVERED,
            DashboardFilter.DELAYED,
            DashboardFilter.EXCEPTIONS,
            DashboardFilter.CARRIER,
            DashboardFilter.DATE_RANGE
        ]
        
        print("✅ Dashboard filter options:")
        for filter_type in filters:
            print(f"   - {filter_type.name}: {filter_type.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Dashboard filter test failed: {e}")
        return False

def test_tracking_system_methods():
    """Test tracking system core methods"""
    print("\n🧪 TESTING TRACKING SYSTEM METHODS")
    print("-" * 40)
    
    try:
        from app import app, db
        from tracking_system import get_tracking_system
        
        with app.app_context():
            tracking_system = get_tracking_system(db.session)
            
            # Test method availability
            methods = [
                'get_tracking_info',
                'start_tracking',
                'stop_tracking',
                'get_tracking_summary',
                'update_tracking_status',
                'get_active_trackings'
            ]
            
            for method_name in methods:
                if hasattr(tracking_system, method_name):
                    print(f"✅ Method available: {method_name}")
                else:
                    print(f"❌ Method missing: {method_name}")
            
            # Test tracking summary
            summary = tracking_system.get_tracking_summary()
            print(f"✅ Tracking summary keys: {list(summary.keys())}")
            
        return True
        
    except Exception as e:
        print(f"❌ Tracking system methods test failed: {e}")
        return False

def test_dashboard_service_methods():
    """Test dashboard service methods"""
    print("\n🧪 TESTING DASHBOARD SERVICE METHODS")
    print("-" * 40)
    
    try:
        from app import app, db
        from tracking_dashboard import TrackingDashboardService
        
        with app.app_context():
            dashboard_service = TrackingDashboardService(db.session)
            
            # Test method availability
            methods = [
                'get_dashboard_metrics',
                'get_shipment_list',
                '_get_basic_metrics',
                '_get_carrier_performance',
                '_get_daily_shipment_volume',
                '_get_status_distribution'
            ]
            
            for method_name in methods:
                if hasattr(dashboard_service, method_name):
                    print(f"✅ Method available: {method_name}")
                else:
                    print(f"❌ Method missing: {method_name}")
            
            # Test metrics structure
            metrics = dashboard_service.get_dashboard_metrics(days=7)
            print(f"✅ Metrics type: {type(metrics).__name__}")
            print(f"✅ Metrics attributes: {[attr for attr in dir(metrics) if not attr.startswith('_')]}")
            
        return True
        
    except Exception as e:
        print(f"❌ Dashboard service methods test failed: {e}")
        return False

def test_database_integration():
    """Test database integration"""
    print("\n🧪 TESTING DATABASE INTEGRATION")
    print("-" * 40)
    
    try:
        from app import app, db, TrackingEvent, Order
        
        with app.app_context():
            # Test TrackingEvent model
            print("✅ TrackingEvent model attributes:")
            tracking_attrs = [attr for attr in dir(TrackingEvent) if not attr.startswith('_') and not callable(getattr(TrackingEvent, attr))]
            for attr in tracking_attrs[:10]:  # Show first 10
                print(f"   - {attr}")
            
            # Test relationships
            if hasattr(TrackingEvent, 'order'):
                print("✅ TrackingEvent.order relationship exists")
            if hasattr(TrackingEvent, 'shipment'):
                print("✅ TrackingEvent.shipment relationship exists")
            
            # Test Order backref
            if hasattr(Order, 'tracking_events'):
                print("✅ Order.tracking_events backref exists")
            
        return True
        
    except Exception as e:
        print(f"❌ Database integration test failed: {e}")
        return False

def run_feature_tests():
    """Run all feature tests"""
    print("🚀 TESTING TRACKING SYSTEM FEATURES")
    print("=" * 50)
    
    tests = [
        ("Status Mapping", test_tracking_status_mapping),
        ("Tracking Enums", test_tracking_enums),
        ("Dashboard Filters", test_dashboard_filters),
        ("Tracking System Methods", test_tracking_system_methods),
        ("Dashboard Service Methods", test_dashboard_service_methods),
        ("Database Integration", test_database_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 FEATURE TEST RESULTS")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
    
    print(f"\n🎯 RESULT: {passed}/{total} feature tests passed")
    return passed == total

if __name__ == "__main__":
    success = run_feature_tests()
    sys.exit(0 if success else 1)
