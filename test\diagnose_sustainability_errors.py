#!/usr/bin/env python3
"""
Sustainability API Error Diagnosis
==================================

Diagnose the actual errors causing the sustainability API to fail.
"""

import sys
import os
import traceback
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_database_connection():
    """Diagnose database connection issues"""
    print("🔍 Diagnosing Database Connection")
    print("=" * 40)
    
    try:
        from app import app, db
        
        with app.app_context():
            print("✅ App context established")
            
            # Test database connection
            try:
                db.engine.execute("SELECT 1")
                print("✅ Database connection: Working")
            except Exception as db_error:
                print(f"❌ Database connection error: {db_error}")
                return False
            
            # Test model imports
            try:
                from app import User, Product, Order, OrderItem, AdminUser
                print("✅ Model imports: Working")
                
                # Test basic queries
                product_count = Product.query.count()
                order_count = Order.query.count()
                print(f"   📊 Products in database: {product_count}")
                print(f"   📊 Orders in database: {order_count}")
                
                if product_count == 0:
                    print("   ⚠️  No products found - this may cause issues")
                if order_count == 0:
                    print("   ⚠️  No orders found - metrics will be empty")
                
            except Exception as model_error:
                print(f"❌ Model import error: {model_error}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Database diagnosis failed: {e}")
        traceback.print_exc()
        return False

def diagnose_sustainability_metrics():
    """Diagnose sustainability metrics calculation"""
    print("\n🧮 Diagnosing Sustainability Metrics")
    print("=" * 45)
    
    try:
        from app import app, db, Product, Order, OrderItem
        from datetime import datetime, timedelta
        
        with app.app_context():
            print("✅ App context established")
            
            # Test date calculation
            days = 30
            start_date = datetime.utcnow() - timedelta(days=days)
            print(f"✅ Date range: {start_date} to {datetime.utcnow()}")
            
            # Test order query
            try:
                orders = Order.query.filter(Order.created_at >= start_date).all()
                print(f"✅ Orders query: Found {len(orders)} orders")
                
                if len(orders) == 0:
                    print("   ⚠️  No orders in date range - creating sample data")
                    return True
                
                # Test order items access
                total_items = 0
                for order in orders:
                    try:
                        items = order.items
                        total_items += len(items)
                        print(f"   📦 Order {order.id}: {len(items)} items")
                        
                        # Test product access
                        for item in items:
                            product = item.product
                            sustainability_score = getattr(product, 'sustainability_score', None)
                            print(f"      🏷️  Product {product.id}: {product.name}")
                            print(f"         Sustainability score: {sustainability_score}")
                            
                            # Test other attributes
                            attrs = ['carbon_footprint', 'recyclable', 'organic', 'carbon_neutral']
                            for attr in attrs:
                                value = getattr(product, attr, 'NOT_FOUND')
                                print(f"         {attr}: {value}")
                            
                            break  # Only check first item
                        break  # Only check first order
                        
                    except Exception as item_error:
                        print(f"   ❌ Error accessing order items: {item_error}")
                        return False
                
                print(f"✅ Total items found: {total_items}")
                
            except Exception as query_error:
                print(f"❌ Order query error: {query_error}")
                traceback.print_exc()
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Metrics diagnosis failed: {e}")
        traceback.print_exc()
        return False

def diagnose_green_heroes():
    """Diagnose green heroes query"""
    print("\n🏆 Diagnosing Green Heroes Query")
    print("=" * 40)
    
    try:
        from app import app, db, Product, Order, OrderItem
        from datetime import datetime, timedelta
        
        with app.app_context():
            print("✅ App context established")
            
            # Test basic query components
            limit = 10
            days = 30
            start_date = datetime.utcnow() - timedelta(days=days)
            
            try:
                # Test simple product query first
                products = Product.query.limit(5).all()
                print(f"✅ Simple product query: Found {len(products)} products")
                
                # Test join query
                join_query = db.session.query(Product).join(
                    OrderItem, Product.id == OrderItem.product_id
                ).join(
                    Order, OrderItem.order_id == Order.id
                ).filter(
                    Order.created_at >= start_date
                ).limit(5).all()
                
                print(f"✅ Join query: Found {len(join_query)} products with orders")
                
                # Test aggregation query
                agg_query = db.session.query(
                    Product,
                    db.func.sum(OrderItem.quantity).label('total_sold')
                ).join(
                    OrderItem, Product.id == OrderItem.product_id
                ).join(
                    Order, OrderItem.order_id == Order.id
                ).filter(
                    Order.created_at >= start_date
                ).group_by(
                    Product.id
                ).limit(5).all()
                
                print(f"✅ Aggregation query: Found {len(agg_query)} products")
                
                for product, total_sold in agg_query:
                    print(f"   📦 {product.name}: {total_sold} sold")
                
            except Exception as query_error:
                print(f"❌ Green heroes query error: {query_error}")
                traceback.print_exc()
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Green heroes diagnosis failed: {e}")
        traceback.print_exc()
        return False

def diagnose_api_endpoints():
    """Diagnose API endpoints directly"""
    print("\n🌐 Diagnosing API Endpoints")
    print("=" * 35)
    
    try:
        from app import app
        from sustainability_api import get_sustainability_metrics, get_green_heroes, get_sustainability_goals
        
        # Test metrics endpoint
        print("🔍 Testing metrics endpoint...")
        with app.test_request_context('/api/sustainability/metrics'):
            try:
                result = get_sustainability_metrics()
                print(f"   Status: {result[1]}")
                if result[1] != 200:
                    response_data = result[0].get_json()
                    print(f"   Error: {response_data}")
                else:
                    print("   ✅ Metrics endpoint working!")
            except Exception as e:
                print(f"   ❌ Metrics endpoint error: {e}")
                traceback.print_exc()
        
        # Test green heroes endpoint
        print("\n🔍 Testing green heroes endpoint...")
        with app.test_request_context('/api/sustainability/green-heroes'):
            try:
                result = get_green_heroes()
                print(f"   Status: {result[1]}")
                if result[1] != 200:
                    response_data = result[0].get_json()
                    print(f"   Error: {response_data}")
                else:
                    print("   ✅ Green heroes endpoint working!")
            except Exception as e:
                print(f"   ❌ Green heroes endpoint error: {e}")
                traceback.print_exc()
        
        # Test goals endpoint
        print("\n🔍 Testing goals endpoint...")
        with app.test_request_context('/api/sustainability/goals'):
            try:
                result = get_sustainability_goals()
                print(f"   Status: {result[1]}")
                if result[1] != 200:
                    response_data = result[0].get_json()
                    print(f"   Error: {response_data}")
                else:
                    print("   ✅ Goals endpoint working!")
            except Exception as e:
                print(f"   ❌ Goals endpoint error: {e}")
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ API endpoint diagnosis failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all diagnostics"""
    print("🚀 Sustainability API Error Diagnosis")
    print("=" * 50)
    print()
    
    diagnostics = [
        diagnose_database_connection,
        diagnose_sustainability_metrics,
        diagnose_green_heroes,
        diagnose_api_endpoints
    ]
    
    results = []
    for diagnostic in diagnostics:
        try:
            result = diagnostic()
            results.append(result)
        except Exception as e:
            print(f"❌ Diagnostic {diagnostic.__name__} failed: {e}")
            results.append(False)
    
    # Summary
    print("\n📊 Diagnosis Results")
    print("=" * 25)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All diagnostics passed! The issue may be elsewhere.")
    else:
        print("\n⚠️  Some diagnostics failed. Check the errors above.")
    
    print("\n🔧 NEXT STEPS:")
    print("   1. Check the server logs for detailed error messages")
    print("   2. Ensure the database has sample data")
    print("   3. Verify all required database columns exist")
    print("   4. Test with a running server instance")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
