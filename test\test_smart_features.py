#!/usr/bin/env python3
"""
Test Smart Features API
=======================

Test the Smart Features API endpoints to ensure they're working properly.

Author: Allora Development Team
Date: 2025-07-11
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_smart_features_endpoints():
    """Test all Smart Features API endpoints"""
    print("🧪 Testing Smart Features API Endpoints")
    print("=" * 60)
    
    endpoints_to_test = [
        '/api/smart-features/overview',
        '/api/smart-features/visual-search-demo',
        '/api/smart-features/price-trends-demo',
        '/api/smart-features/inventory-insights-demo',
        '/api/smart-features/personalized-recommendations-demo'
    ]
    
    for endpoint in endpoints_to_test:
        try:
            print(f"Testing: {endpoint}")
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"  ✅ Success: {endpoint}")
                    if 'data' in data:
                        print(f"     Data keys: {list(data['data'].keys())}")
                else:
                    print(f"  ⚠️  Response not successful: {data}")
            elif response.status_code == 404:
                print(f"  ❌ Not Found: {endpoint}")
            else:
                print(f"  ⚠️  Status: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"     Error: {error_data}")
                except:
                    print(f"     Raw response: {response.text[:100]}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Request Error: {e}")
        
        time.sleep(0.5)  # Small delay between requests
    
    return True

def test_existing_ml_endpoints():
    """Test existing ML endpoints to compare"""
    print("\n🤖 Testing Existing ML Endpoints")
    print("=" * 60)
    
    endpoints_to_test = [
        '/api/inventory_predictions',
        '/api/price_trends',
        '/api/advanced_inventory_predictions',
        '/api/advanced_price_trends'
    ]
    
    for endpoint in endpoints_to_test:
        try:
            print(f"Testing: {endpoint}")
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ Success: {len(data) if isinstance(data, list) else 'object'} items")
            else:
                print(f"  ⚠️  Status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Request Error: {e}")
        
        time.sleep(0.5)
    
    return True

def main():
    print("🚀 SMART FEATURES API TESTING")
    print("=" * 60)
    
    # Test Smart Features endpoints
    if not test_smart_features_endpoints():
        print("❌ Smart Features API tests failed")
        return False
    
    # Test existing ML endpoints for comparison
    if not test_existing_ml_endpoints():
        print("❌ Existing ML API tests failed")
        return False
    
    print("\n" + "=" * 60)
    print("✅ SMART FEATURES API TESTING COMPLETE!")
    print("=" * 60)
    
    return True

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
