#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, jsonify

# Create a simple Flask app to test basic functionality
debug_app = Flask(__name__)

@debug_app.route('/', methods=['GET'])
def debug_home():
    return jsonify({'message': 'Debug Flask app is working!', 'status': 'success'})

@debug_app.route('/test', methods=['GET'])
def debug_test():
    return jsonify({'message': 'Test endpoint working!', 'status': 'success'})

if __name__ == '__main__':
    print("Starting debug Flask app on port 5001...")
    debug_app.run(debug=True, host='127.0.0.1', port=5001)
