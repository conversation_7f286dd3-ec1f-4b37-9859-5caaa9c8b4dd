#!/usr/bin/env python3
"""
Comprehensive Database Analysis and Testing Script for Allora E-commerce
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
import logging
from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseAnalyzer:
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.recommendations = []
        
    def log_issue(self, category, message, severity="ERROR"):
        issue = {
            'category': category,
            'message': message,
            'severity': severity,
            'timestamp': datetime.now().isoformat()
        }
        if severity == "ERROR":
            self.issues.append(issue)
        elif severity == "WARNING":
            self.warnings.append(issue)
        else:
            self.recommendations.append(issue)
        
        print(f"🔴 {severity}: [{category}] {message}")

    def analyze_table_structure(self):
        """Analyze all tables and their structure"""
        print("\n" + "="*80)
        print("📊 DATABASE STRUCTURE ANALYSIS")
        print("="*80)
        
        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            print(f"📋 Total Tables: {len(tables)}")
            
            # Check for duplicate table patterns
            table_patterns = {}
            for table in tables:
                base_name = table.rstrip('s')  # Remove trailing 's'
                if base_name in table_patterns:
                    table_patterns[base_name].append(table)
                else:
                    table_patterns[base_name] = [table]
            
            # Find potential duplicates
            duplicates = {k: v for k, v in table_patterns.items() if len(v) > 1}
            if duplicates:
                self.log_issue("STRUCTURE", f"Potential duplicate tables found: {duplicates}", "WARNING")
            
            # Analyze each table
            table_analysis = {}
            for table_name in tables:
                try:
                    columns = inspector.get_columns(table_name)
                    indexes = inspector.get_indexes(table_name)
                    foreign_keys = inspector.get_foreign_keys(table_name)
                    
                    # Count records
                    result = db.session.execute(text(f"SELECT COUNT(*) FROM `{table_name}`"))
                    record_count = result.scalar()
                    
                    table_analysis[table_name] = {
                        'columns': len(columns),
                        'indexes': len(indexes),
                        'foreign_keys': len(foreign_keys),
                        'records': record_count,
                        'column_details': columns
                    }
                    
                    # Check for missing primary keys
                    has_primary_key = any(col.get('primary_key', False) for col in columns)
                    if not has_primary_key:
                        self.log_issue("STRUCTURE", f"Table '{table_name}' has no primary key", "ERROR")
                    
                    # Check for missing indexes on foreign keys
                    fk_columns = [fk['constrained_columns'][0] for fk in foreign_keys if fk['constrained_columns']]
                    indexed_columns = [idx['column_names'][0] for idx in indexes if idx['column_names']]
                    
                    for fk_col in fk_columns:
                        if fk_col not in indexed_columns:
                            self.log_issue("PERFORMANCE", f"Foreign key '{fk_col}' in table '{table_name}' lacks index", "WARNING")
                    
                except Exception as e:
                    self.log_issue("STRUCTURE", f"Error analyzing table '{table_name}': {e}", "ERROR")
            
            return table_analysis

    def analyze_data_integrity(self):
        """Check data integrity across tables"""
        print("\n" + "="*80)
        print("🔍 DATA INTEGRITY ANALYSIS")
        print("="*80)
        
        with app.app_context():
            # Check for orphaned records
            integrity_checks = [
                {
                    'name': 'Orphaned Cart Items',
                    'query': """
                        SELECT COUNT(*) FROM cart_item ci 
                        LEFT JOIN user u ON ci.user_id = u.id 
                        WHERE u.id IS NULL AND ci.user_id IS NOT NULL
                    """
                },
                {
                    'name': 'Orphaned Order Items',
                    'query': """
                        SELECT COUNT(*) FROM order_item oi 
                        LEFT JOIN orders o ON oi.order_id = o.id 
                        WHERE o.id IS NULL
                    """
                },
                {
                    'name': 'Products without Sellers',
                    'query': """
                        SELECT COUNT(*) FROM product p 
                        LEFT JOIN seller s ON p.seller_id = s.id 
                        WHERE s.id IS NULL AND p.seller_id IS NOT NULL
                    """
                },
                {
                    'name': 'Invalid Stock Quantities',
                    'query': """
                        SELECT COUNT(*) FROM product 
                        WHERE stock_quantity < 0
                    """
                },
                {
                    'name': 'Invalid Prices',
                    'query': """
                        SELECT COUNT(*) FROM product 
                        WHERE price <= 0
                    """
                },
                {
                    'name': 'Missing Product Images',
                    'query': """
                        SELECT COUNT(*) FROM product 
                        WHERE image IS NULL OR image = ''
                    """
                }
            ]
            
            for check in integrity_checks:
                try:
                    result = db.session.execute(text(check['query']))
                    count = result.scalar()
                    if count > 0:
                        self.log_issue("DATA_INTEGRITY", f"{check['name']}: {count} issues found", "WARNING")
                    else:
                        print(f"✅ {check['name']}: No issues")
                except Exception as e:
                    self.log_issue("DATA_INTEGRITY", f"Error checking {check['name']}: {e}", "ERROR")

    def analyze_performance(self):
        """Analyze database performance aspects"""
        print("\n" + "="*80)
        print("⚡ PERFORMANCE ANALYSIS")
        print("="*80)
        
        with app.app_context():
            # Check table sizes
            try:
                result = db.session.execute(text("""
                    SELECT 
                        table_name,
                        table_rows,
                        ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size_MB'
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE()
                    ORDER BY (data_length + index_length) DESC
                    LIMIT 10
                """))
                
                print("📊 Top 10 Largest Tables:")
                for row in result:
                    print(f"  - {row[0]}: {row[1]} rows, {row[2]} MB")
                    
                    # Flag large tables without proper indexing
                    if row[1] > 10000:  # More than 10k rows
                        inspector = inspect(db.engine)
                        indexes = inspector.get_indexes(row[0])
                        if len(indexes) < 2:  # Less than 2 indexes (including primary key)
                            self.log_issue("PERFORMANCE", f"Large table '{row[0]}' ({row[1]} rows) has insufficient indexes", "WARNING")
                            
            except Exception as e:
                self.log_issue("PERFORMANCE", f"Error analyzing table sizes: {e}", "ERROR")

    def test_model_relationships(self):
        """Test SQLAlchemy model relationships"""
        print("\n" + "="*80)
        print("🔗 MODEL RELATIONSHIPS TESTING")
        print("="*80)
        
        with app.app_context():
            try:
                # Import all models from app
                from app import (
                    User, Product, Order, OrderItem, Seller, 
                    ProductReview, CartItem, Wishlist
                )
                
                # Test basic model creation and relationships
                test_cases = [
                    {
                        'name': 'User Model',
                        'test': lambda: User.query.first()
                    },
                    {
                        'name': 'Product Model',
                        'test': lambda: Product.query.first()
                    },
                    {
                        'name': 'Order Model',
                        'test': lambda: Order.query.first()
                    },
                    {
                        'name': 'Seller Model',
                        'test': lambda: Seller.query.first()
                    }
                ]
                
                for test_case in test_cases:
                    try:
                        result = test_case['test']()
                        print(f"✅ {test_case['name']}: Model accessible")
                    except Exception as e:
                        self.log_issue("MODELS", f"{test_case['name']}: {e}", "ERROR")
                        
            except ImportError as e:
                self.log_issue("MODELS", f"Error importing models: {e}", "ERROR")

    def check_constraints_and_triggers(self):
        """Check database constraints and triggers"""
        print("\n" + "="*80)
        print("🛡️ CONSTRAINTS AND TRIGGERS ANALYSIS")
        print("="*80)
        
        with app.app_context():
            try:
                # Check foreign key constraints
                result = db.session.execute(text("""
                    SELECT 
                        TABLE_NAME,
                        CONSTRAINT_NAME,
                        REFERENCED_TABLE_NAME,
                        REFERENCED_COLUMN_NAME
                    FROM information_schema.KEY_COLUMN_USAGE 
                    WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                """))
                
                fk_count = 0
                for row in result:
                    fk_count += 1
                
                print(f"🔗 Foreign Key Constraints: {fk_count}")
                
                # Check for missing constraints on critical relationships
                critical_relationships = [
                    ('order_item', 'order_id', 'orders'),
                    ('order_item', 'product_id', 'product'),
                    ('cart_item', 'user_id', 'user'),
                    ('cart_item', 'product_id', 'product'),
                    ('product_review', 'user_id', 'user'),
                    ('product_review', 'product_id', 'product')
                ]
                
                for table, column, ref_table in critical_relationships:
                    check_query = f"""
                        SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE 
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = '{table}'
                        AND COLUMN_NAME = '{column}'
                        AND REFERENCED_TABLE_NAME = '{ref_table}'
                    """
                    result = db.session.execute(text(check_query))
                    if result.scalar() == 0:
                        self.log_issue("CONSTRAINTS", f"Missing FK constraint: {table}.{column} -> {ref_table}", "WARNING")
                
            except Exception as e:
                self.log_issue("CONSTRAINTS", f"Error checking constraints: {e}", "ERROR")

    def generate_report(self):
        """Generate comprehensive analysis report"""
        print("\n" + "="*80)
        print("📋 COMPREHENSIVE DATABASE ANALYSIS REPORT")
        print("="*80)
        
        print(f"\n🔴 CRITICAL ISSUES: {len(self.issues)}")
        for issue in self.issues:
            print(f"  - [{issue['category']}] {issue['message']}")
        
        print(f"\n⚠️  WARNINGS: {len(self.warnings)}")
        for warning in self.warnings:
            print(f"  - [{warning['category']}] {warning['message']}")
        
        print(f"\n💡 RECOMMENDATIONS: {len(self.recommendations)}")
        for rec in self.recommendations:
            print(f"  - [{rec['category']}] {rec['message']}")
        
        # Save detailed report to file
        report = {
            'timestamp': datetime.now().isoformat(),
            'issues': self.issues,
            'warnings': self.warnings,
            'recommendations': self.recommendations
        }
        
        with open('database_analysis_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: database_analysis_report.json")

def main():
    print("🚀 Starting Comprehensive Database Analysis...")
    
    analyzer = DatabaseAnalyzer()
    
    # Run all analysis modules
    table_analysis = analyzer.analyze_table_structure()
    analyzer.analyze_data_integrity()
    analyzer.analyze_performance()
    analyzer.test_model_relationships()
    analyzer.check_constraints_and_triggers()
    
    # Generate final report
    analyzer.generate_report()
    
    print("\n✅ Database analysis completed!")

if __name__ == '__main__':
    main()
