#!/usr/bin/env python3
"""
Update Product Sustainability Scores
===================================

Updates existing products with sustainability scores and eco-friendly attributes
to test the sustainability impact dashboard.

Author: Allora Development Team
Date: 2025-07-11
"""

import sys
import os
import random

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Flask app and models
from app import app, db, Product

def update_product_sustainability():
    """Update existing products with sustainability data"""
    
    print("🌱 Updating products with sustainability scores...")
    
    with app.app_context():
        try:
            # Get all products
            products = Product.query.all()
            
            if not products:
                print("❌ No products found in database")
                return False
            
            print(f"📦 Found {len(products)} products to update")
            
            # Sustainability data for different product types
            sustainability_profiles = [
                {
                    'sustainability_score': 95,
                    'carbon_footprint': 0.5,
                    'organic': True,
                    'carbon_neutral': True,
                    'recyclable': True
                },
                {
                    'sustainability_score': 88,
                    'carbon_footprint': 1.2,
                    'organic': True,
                    'carbon_neutral': False,
                    'recyclable': True
                },
                {
                    'sustainability_score': 82,
                    'carbon_footprint': 2.1,
                    'organic': False,
                    'carbon_neutral': True,
                    'recyclable': True
                },
                {
                    'sustainability_score': 75,
                    'carbon_footprint': 3.5,
                    'organic': False,
                    'carbon_neutral': False,
                    'recyclable': True
                },
                {
                    'sustainability_score': 65,
                    'carbon_footprint': 5.2,
                    'organic': False,
                    'carbon_neutral': False,
                    'recyclable': False
                }
            ]
            
            updated_count = 0
            
            for product in products:
                # Randomly assign a sustainability profile
                profile = random.choice(sustainability_profiles)
                
                # Update product with sustainability data
                product.sustainability_score = profile['sustainability_score']
                
                # Add sustainability attributes if they don't exist
                if not hasattr(product, 'carbon_footprint'):
                    # Add as dynamic attribute (in production, add to model)
                    setattr(product, 'carbon_footprint', profile['carbon_footprint'])
                
                if not hasattr(product, 'organic'):
                    setattr(product, 'organic', profile['organic'])
                
                if not hasattr(product, 'carbon_neutral'):
                    setattr(product, 'carbon_neutral', profile['carbon_neutral'])
                
                if not hasattr(product, 'recyclable'):
                    setattr(product, 'recyclable', profile['recyclable'])
                
                updated_count += 1
                
                print(f"✅ Updated {product.name}: Sustainability Score {profile['sustainability_score']}")
            
            db.session.commit()
            print(f"\n🎉 Successfully updated {updated_count} products with sustainability data!")
            
            # Show summary
            high_sustainability = Product.query.filter(Product.sustainability_score >= 80).count()
            medium_sustainability = Product.query.filter(
                Product.sustainability_score >= 60,
                Product.sustainability_score < 80
            ).count()
            low_sustainability = Product.query.filter(Product.sustainability_score < 60).count()
            
            print(f"\n📊 Sustainability Summary:")
            print(f"   High Sustainability (80+): {high_sustainability} products")
            print(f"   Medium Sustainability (60-79): {medium_sustainability} products")
            print(f"   Low Sustainability (<60): {low_sustainability} products")
            
            return True
            
        except Exception as e:
            print(f"❌ Error updating product sustainability: {e}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    print("🌱 Allora Product Sustainability Updater")
    print("=" * 50)
    
    if update_product_sustainability():
        print("\n✅ Product sustainability update completed!")
        print("\nNext steps:")
        print("1. Run 'python add_sample_orders.py' to create sample orders")
        print("2. Test the sustainability dashboard at http://localhost:3000")
    else:
        print("\n❌ Failed to update product sustainability")
        sys.exit(1)
