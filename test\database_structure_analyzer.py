#!/usr/bin/env python3
"""
Database Structure Analyzer for Allora E-commerce Project
=========================================================

This script analyzes and displays the complete database structure including:
1. All tables with their columns and data types
2. Primary keys and foreign key relationships
3. Indexes and constraints
4. Table relationships and dependencies
5. Database schema visualization

Usage:
    python database_structure_analyzer.py                    # Full structure analysis
    python database_structure_analyzer.py --tables-only      # Tables and columns only
    python database_structure_analyzer.py --relationships    # Focus on relationships
    python database_structure_analyzer.py --export           # Export to JSON/SQL files
    python database_structure_analyzer.py --table user       # Analyze specific table

Author: Allora Development Team
Date: 2025-01-07
"""

import os
import sys
import argparse
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text, inspect, MetaData
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

class DatabaseStructureAnalyzer:
    """Database structure analysis utility class"""
    
    def __init__(self):
        self.inspector = None
        self.tables_info = {}
        self.relationships = []
        self.indexes_info = {}
        self.constraints_info = {}
        self.database_stats = {}
        
    def initialize_inspector(self):
        """Initialize the database inspector"""
        try:
            self.inspector = inspect(db.engine)
            return True
        except Exception as e:
            print(f"❌ Failed to initialize database inspector: {e}")
            return False
    
    def get_table_structure(self, table_name):
        """Get detailed structure information for a table"""
        try:
            # Get columns
            columns = self.inspector.get_columns(table_name)
            
            # Get primary keys
            pk_constraint = self.inspector.get_pk_constraint(table_name)
            primary_keys = pk_constraint['constrained_columns'] if pk_constraint else []
            
            # Get foreign keys
            foreign_keys = self.inspector.get_foreign_keys(table_name)
            
            # Get indexes
            indexes = self.inspector.get_indexes(table_name)
            
            # Get unique constraints
            unique_constraints = self.inspector.get_unique_constraints(table_name)
            
            # Get check constraints (if supported)
            check_constraints = []
            try:
                check_constraints = self.inspector.get_check_constraints(table_name)
            except:
                pass  # Not all databases support this
            
            table_info = {
                'name': table_name,
                'columns': [],
                'primary_keys': primary_keys,
                'foreign_keys': [],
                'indexes': [],
                'unique_constraints': [],
                'check_constraints': check_constraints
            }
            
            # Process columns
            for col in columns:
                column_info = {
                    'name': col['name'],
                    'type': str(col['type']),
                    'nullable': col['nullable'],
                    'default': str(col['default']) if col['default'] is not None else None,
                    'autoincrement': col.get('autoincrement', False),
                    'primary_key': col['name'] in primary_keys
                }
                table_info['columns'].append(column_info)
            
            # Process foreign keys
            for fk in foreign_keys:
                fk_info = {
                    'name': fk.get('name', 'unnamed'),
                    'constrained_columns': fk['constrained_columns'],
                    'referred_table': fk['referred_table'],
                    'referred_columns': fk['referred_columns'],
                    'ondelete': fk.get('ondelete'),
                    'onupdate': fk.get('onupdate')
                }
                table_info['foreign_keys'].append(fk_info)
                
                # Add to relationships list
                relationship = {
                    'from_table': table_name,
                    'from_columns': fk['constrained_columns'],
                    'to_table': fk['referred_table'],
                    'to_columns': fk['referred_columns'],
                    'constraint_name': fk.get('name', 'unnamed')
                }
                self.relationships.append(relationship)
            
            # Process indexes
            for idx in indexes:
                idx_info = {
                    'name': idx['name'],
                    'columns': idx['column_names'],
                    'unique': idx['unique']
                }
                table_info['indexes'].append(idx_info)
            
            # Process unique constraints
            for uc in unique_constraints:
                uc_info = {
                    'name': uc['name'],
                    'columns': uc['column_names']
                }
                table_info['unique_constraints'].append(uc_info)
            
            return table_info
            
        except Exception as e:
            print(f"❌ Error analyzing table {table_name}: {e}")
            return None
    
    def analyze_all_tables(self):
        """Analyze all tables in the database"""
        print("\n" + "="*80)
        print("🔍 ANALYZING DATABASE STRUCTURE")
        print("="*80)
        
        if not self.initialize_inspector():
            return False
        
        try:
            # Get all table names
            tables = self.inspector.get_table_names()
            tables = [table for table in tables if not table.startswith('alembic')]
            tables.sort()
            
            if not tables:
                print("❌ No tables found in database")
                return False
            
            print(f"📋 Found {len(tables)} tables to analyze")
            print("🚀 Starting structure analysis...\n")
            
            # Analyze each table
            for i, table in enumerate(tables, 1):
                print(f"[{i:2d}/{len(tables)}] Analyzing table: {table}")
                
                table_info = self.get_table_structure(table)
                if table_info:
                    self.tables_info[table] = table_info
                    print(f"         📊 {len(table_info['columns'])} columns, "
                          f"{len(table_info['foreign_keys'])} foreign keys, "
                          f"{len(table_info['indexes'])} indexes")
                else:
                    print(f"         ❌ Failed to analyze")
            
            # Calculate database statistics
            self.calculate_database_stats()
            
            print(f"\n✅ Structure analysis complete!")
            return True
            
        except Exception as e:
            print(f"❌ Error during structure analysis: {e}")
            return False
    
    def calculate_database_stats(self):
        """Calculate overall database statistics"""
        total_tables = len(self.tables_info)
        total_columns = sum(len(table['columns']) for table in self.tables_info.values())
        total_foreign_keys = sum(len(table['foreign_keys']) for table in self.tables_info.values())
        total_indexes = sum(len(table['indexes']) for table in self.tables_info.values())
        total_relationships = len(self.relationships)
        
        # Find tables with most columns
        tables_by_columns = sorted(
            [(name, len(info['columns'])) for name, info in self.tables_info.items()],
            key=lambda x: x[1], reverse=True
        )
        
        # Find tables with most relationships
        relationship_counts = {}
        for rel in self.relationships:
            from_table = rel['from_table']
            to_table = rel['to_table']
            relationship_counts[from_table] = relationship_counts.get(from_table, 0) + 1
            relationship_counts[to_table] = relationship_counts.get(to_table, 0) + 1
        
        tables_by_relationships = sorted(
            relationship_counts.items(), key=lambda x: x[1], reverse=True
        )
        
        self.database_stats = {
            'total_tables': total_tables,
            'total_columns': total_columns,
            'total_foreign_keys': total_foreign_keys,
            'total_indexes': total_indexes,
            'total_relationships': total_relationships,
            'tables_by_columns': tables_by_columns[:10],  # Top 10
            'tables_by_relationships': tables_by_relationships[:10]  # Top 10
        }
    
    def display_database_overview(self):
        """Display database overview and statistics"""
        print("\n" + "="*80)
        print("📊 DATABASE OVERVIEW")
        print("="*80)
        
        stats = self.database_stats
        print(f"🗄️  Total Tables: {stats['total_tables']}")
        print(f"📋 Total Columns: {stats['total_columns']}")
        print(f"🔗 Total Foreign Keys: {stats['total_foreign_keys']}")
        print(f"📇 Total Indexes: {stats['total_indexes']}")
        print(f"🔄 Total Relationships: {stats['total_relationships']}")
        
        print(f"\n📈 TABLES BY COLUMN COUNT (Top 10):")
        for table, count in stats['tables_by_columns']:
            print(f"   📊 {table}: {count} columns")
        
        if stats['tables_by_relationships']:
            print(f"\n🔗 TABLES BY RELATIONSHIP COUNT (Top 10):")
            for table, count in stats['tables_by_relationships']:
                print(f"   🔄 {table}: {count} relationships")
    
    def display_table_details(self, table_name=None):
        """Display detailed information for all tables or a specific table"""
        if table_name:
            tables_to_show = [table_name] if table_name in self.tables_info else []
            if not tables_to_show:
                print(f"❌ Table '{table_name}' not found")
                return
            title = f"TABLE DETAILS: {table_name}"
        else:
            tables_to_show = sorted(self.tables_info.keys())
            title = "ALL TABLE DETAILS"
        
        print("\n" + "="*80)
        print(f"📋 {title}")
        print("="*80)
        
        for table in tables_to_show:
            table_info = self.tables_info[table]
            
            print(f"\n🗂️  TABLE: {table}")
            print("-" * 60)
            
            # Columns
            print(f"📋 COLUMNS ({len(table_info['columns'])}):")
            for col in table_info['columns']:
                pk_marker = " 🔑" if col['primary_key'] else ""
                nullable = "NULL" if col['nullable'] else "NOT NULL"
                default = f" DEFAULT {col['default']}" if col['default'] else ""
                autoincrement = " AUTO_INCREMENT" if col['autoincrement'] else ""
                
                print(f"   📊 {col['name']}: {col['type']} {nullable}{default}{autoincrement}{pk_marker}")
            
            # Primary Keys
            if table_info['primary_keys']:
                print(f"\n🔑 PRIMARY KEYS:")
                for pk in table_info['primary_keys']:
                    print(f"   🔑 {pk}")
            
            # Foreign Keys
            if table_info['foreign_keys']:
                print(f"\n🔗 FOREIGN KEYS ({len(table_info['foreign_keys'])}):")
                for fk in table_info['foreign_keys']:
                    columns = ', '.join(fk['constrained_columns'])
                    ref_columns = ', '.join(fk['referred_columns'])
                    print(f"   🔗 {fk['name']}: {columns} → {fk['referred_table']}.{ref_columns}")
                    if fk['ondelete'] or fk['onupdate']:
                        actions = []
                        if fk['ondelete']:
                            actions.append(f"ON DELETE {fk['ondelete']}")
                        if fk['onupdate']:
                            actions.append(f"ON UPDATE {fk['onupdate']}")
                        print(f"      {' '.join(actions)}")
            
            # Indexes
            if table_info['indexes']:
                print(f"\n📇 INDEXES ({len(table_info['indexes'])}):")
                for idx in table_info['indexes']:
                    unique = " (UNIQUE)" if idx['unique'] else ""
                    columns = ', '.join(idx['columns'])
                    print(f"   📇 {idx['name']}: {columns}{unique}")
            
            # Unique Constraints
            if table_info['unique_constraints']:
                print(f"\n🔒 UNIQUE CONSTRAINTS ({len(table_info['unique_constraints'])}):")
                for uc in table_info['unique_constraints']:
                    columns = ', '.join(uc['columns'])
                    print(f"   🔒 {uc['name']}: {columns}")
    
    def display_relationships(self):
        """Display all table relationships"""
        print("\n" + "="*80)
        print("🔄 DATABASE RELATIONSHIPS")
        print("="*80)
        
        if not self.relationships:
            print("📭 No foreign key relationships found")
            return
        
        print(f"🔗 Found {len(self.relationships)} relationships:\n")
        
        # Group relationships by from_table
        relationships_by_table = {}
        for rel in self.relationships:
            from_table = rel['from_table']
            if from_table not in relationships_by_table:
                relationships_by_table[from_table] = []
            relationships_by_table[from_table].append(rel)
        
        for from_table in sorted(relationships_by_table.keys()):
            print(f"📊 {from_table}:")
            for rel in relationships_by_table[from_table]:
                from_cols = ', '.join(rel['from_columns'])
                to_cols = ', '.join(rel['to_columns'])
                print(f"   🔗 {from_cols} → {rel['to_table']}.{to_cols}")
                print(f"      (Constraint: {rel['constraint_name']})")
            print()
    
    def generate_create_statements(self):
        """Generate CREATE TABLE statements for the database schema"""
        print("\n" + "="*80)
        print("🛠️  CREATE TABLE STATEMENTS")
        print("="*80)
        
        for table_name in sorted(self.tables_info.keys()):
            table_info = self.tables_info[table_name]
            
            print(f"\n-- Table: {table_name}")
            print(f"CREATE TABLE `{table_name}` (")
            
            # Columns
            column_definitions = []
            for col in table_info['columns']:
                col_def = f"  `{col['name']}` {col['type']}"
                
                if not col['nullable']:
                    col_def += " NOT NULL"
                
                if col['default'] is not None and col['default'] != 'None':
                    col_def += f" DEFAULT {col['default']}"
                
                if col['autoincrement']:
                    col_def += " AUTO_INCREMENT"
                
                column_definitions.append(col_def)
            
            print(',\n'.join(column_definitions))
            
            # Primary key
            if table_info['primary_keys']:
                pk_cols = ', '.join([f"`{pk}`" for pk in table_info['primary_keys']])
                print(f",\n  PRIMARY KEY ({pk_cols})")
            
            print(");")
            
            # Foreign key constraints
            for fk in table_info['foreign_keys']:
                constrained_cols = ', '.join([f"`{col}`" for col in fk['constrained_columns']])
                referred_cols = ', '.join([f"`{col}`" for col in fk['referred_columns']])
                
                print(f"ALTER TABLE `{table_name}` ADD CONSTRAINT `{fk['name']}`")
                print(f"  FOREIGN KEY ({constrained_cols})")
                print(f"  REFERENCES `{fk['referred_table']}` ({referred_cols})")
                
                if fk['ondelete']:
                    print(f"  ON DELETE {fk['ondelete']}")
                if fk['onupdate']:
                    print(f"  ON UPDATE {fk['onupdate']}")
                print(";")
            
            # Indexes
            for idx in table_info['indexes']:
                if idx['name'].startswith('PRIMARY'):
                    continue  # Skip primary key indexes
                
                unique = "UNIQUE " if idx['unique'] else ""
                idx_cols = ', '.join([f"`{col}`" for col in idx['columns']])
                print(f"CREATE {unique}INDEX `{idx['name']}` ON `{table_name}` ({idx_cols});")
            
            print()
    
    def export_to_json(self, filename=None):
        """Export database structure to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"database_structure_{timestamp}.json"
        
        try:
            structure_data = {
                'generated_at': datetime.now().isoformat(),
                'database_stats': self.database_stats,
                'tables': self.tables_info,
                'relationships': self.relationships
            }
            
            filepath = os.path.join(os.path.dirname(__file__), filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(structure_data, f, indent=2, ensure_ascii=False)
            
            print(f"📄 Structure exported to JSON: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to export to JSON: {e}")
            return False
    
    def export_to_sql(self, filename=None):
        """Export database structure to SQL file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"database_schema_{timestamp}.sql"
        
        try:
            filepath = os.path.join(os.path.dirname(__file__), filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"-- Allora Database Schema\n")
                f.write(f"-- Generated: {datetime.now()}\n")
                f.write(f"-- Total Tables: {self.database_stats['total_tables']}\n\n")
                
                # Write CREATE TABLE statements
                for table_name in sorted(self.tables_info.keys()):
                    table_info = self.tables_info[table_name]
                    
                    f.write(f"-- Table: {table_name}\n")
                    f.write(f"CREATE TABLE `{table_name}` (\n")
                    
                    # Columns
                    column_definitions = []
                    for col in table_info['columns']:
                        col_def = f"  `{col['name']}` {col['type']}"
                        
                        if not col['nullable']:
                            col_def += " NOT NULL"
                        
                        if col['default'] is not None and col['default'] != 'None':
                            col_def += f" DEFAULT {col['default']}"
                        
                        if col['autoincrement']:
                            col_def += " AUTO_INCREMENT"
                        
                        column_definitions.append(col_def)
                    
                    f.write(',\n'.join(column_definitions))
                    
                    # Primary key
                    if table_info['primary_keys']:
                        pk_cols = ', '.join([f"`{pk}`" for pk in table_info['primary_keys']])
                        f.write(f",\n  PRIMARY KEY ({pk_cols})")
                    
                    f.write("\n);\n\n")
                
                # Write foreign key constraints
                f.write("-- Foreign Key Constraints\n")
                for table_name, table_info in self.tables_info.items():
                    for fk in table_info['foreign_keys']:
                        constrained_cols = ', '.join([f"`{col}`" for col in fk['constrained_columns']])
                        referred_cols = ', '.join([f"`{col}`" for col in fk['referred_columns']])
                        
                        f.write(f"ALTER TABLE `{table_name}` ADD CONSTRAINT `{fk['name']}`\n")
                        f.write(f"  FOREIGN KEY ({constrained_cols})\n")
                        f.write(f"  REFERENCES `{fk['referred_table']}` ({referred_cols})")
                        
                        if fk['ondelete']:
                            f.write(f"\n  ON DELETE {fk['ondelete']}")
                        if fk['onupdate']:
                            f.write(f"\n  ON UPDATE {fk['onupdate']}")
                        f.write(";\n\n")
                
                # Write indexes
                f.write("-- Indexes\n")
                for table_name, table_info in self.tables_info.items():
                    for idx in table_info['indexes']:
                        if idx['name'].startswith('PRIMARY'):
                            continue
                        
                        unique = "UNIQUE " if idx['unique'] else ""
                        idx_cols = ', '.join([f"`{col}`" for col in idx['columns']])
                        f.write(f"CREATE {unique}INDEX `{idx['name']}` ON `{table_name}` ({idx_cols});\n")
                
            print(f"📄 Structure exported to SQL: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to export to SQL: {e}")
            return False


def main():
    """Main function to handle command line arguments and execute structure analysis"""
    parser = argparse.ArgumentParser(
        description='Analyze Allora database structure',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python database_structure_analyzer.py                    # Full structure analysis
  python database_structure_analyzer.py --tables-only      # Tables and columns only
  python database_structure_analyzer.py --relationships    # Focus on relationships
  python database_structure_analyzer.py --table user       # Analyze specific table
  python database_structure_analyzer.py --export           # Export to JSON/SQL files
  python database_structure_analyzer.py --create-sql       # Show CREATE statements
        """
    )

    parser.add_argument('--tables-only', action='store_true',
                       help='Show tables and columns only')
    parser.add_argument('--relationships', action='store_true',
                       help='Focus on table relationships')
    parser.add_argument('--table', type=str,
                       help='Analyze specific table in detail')
    parser.add_argument('--export', action='store_true',
                       help='Export structure to JSON and SQL files')
    parser.add_argument('--create-sql', action='store_true',
                       help='Show CREATE TABLE statements')
    parser.add_argument('--overview-only', action='store_true',
                       help='Show database overview only')

    args = parser.parse_args()

    print("🚀 Allora Database Structure Analyzer")
    print("="*60)

    # Initialize analyzer
    analyzer = DatabaseStructureAnalyzer()

    try:
        with app.app_context():
            # Analyze database structure
            if not analyzer.analyze_all_tables():
                print("\n❌ Database structure analysis failed")
                sys.exit(1)

            # Display results based on arguments
            if args.overview_only:
                analyzer.display_database_overview()
            elif args.table:
                analyzer.display_database_overview()
                analyzer.display_table_details(args.table)
            elif args.relationships:
                analyzer.display_database_overview()
                analyzer.display_relationships()
            elif args.create_sql:
                analyzer.display_database_overview()
                analyzer.generate_create_statements()
            elif args.tables_only:
                analyzer.display_database_overview()
                analyzer.display_table_details()
            else:
                # Default: show everything
                analyzer.display_database_overview()
                analyzer.display_table_details()
                analyzer.display_relationships()

            # Export if requested
            if args.export:
                print("\n" + "="*60)
                print("📤 EXPORTING STRUCTURE")
                print("="*60)
                analyzer.export_to_json()
                analyzer.export_to_sql()

            print("\n🎉 Database structure analysis completed successfully!")

    except Exception as e:
        print(f"\n❌ Fatal error during structure analysis: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
