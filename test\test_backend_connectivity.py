#!/usr/bin/env python3
"""
Backend Connectivity Test Script
================================

This script tests if the backend is running and accessible.
It checks various endpoints and provides detailed diagnostics.
"""

import requests
import json
import time
import sys
from datetime import datetime

def test_endpoint(url, method='GET', data=None, timeout=10):
    """Test a specific endpoint"""
    try:
        print(f"🔍 Testing {method} {url}")
        
        if method == 'GET':
            response = requests.get(url, timeout=timeout)
        elif method == 'POST':
            response = requests.post(url, json=data, timeout=timeout)
        else:
            print(f"❌ Unsupported method: {method}")
            return False
        
        print(f"   Status: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print(f"   Response: {json.dumps(json_data, indent=2)}")
            except:
                print(f"   Response: {response.text[:200]}...")
            print("   ✅ SUCCESS")
            return True
        else:
            print(f"   ❌ FAILED: {response.status_code} - {response.text[:100]}")
            return False
            
    except requests.exceptions.ConnectRefused:
        print(f"   ❌ CONNECTION REFUSED - Backend not running on this port")
        return False
    except requests.exceptions.Timeout:
        print(f"   ❌ TIMEOUT - Backend is running but not responding")
        return False
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

def main():
    print("🚀 Backend Connectivity Test")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    base_url = "http://localhost:5000"
    
    # Test endpoints
    endpoints = [
        ("/", "GET"),
        ("/api/health", "GET"),
        ("/api/oauth/providers", "GET"),
        ("/api/products", "GET"),
    ]
    
    results = []
    
    for endpoint, method in endpoints:
        url = base_url + endpoint
        success = test_endpoint(url, method)
        results.append((endpoint, success))
        print()
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print("📊 SUMMARY")
    print("=" * 30)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for endpoint, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{endpoint:20} {status}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == 0:
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Check if backend is running:")
        print("   cd allora/backend")
        print("   python run_with_waitress.py")
        print("\n2. Check if port 5000 is in use:")
        print("   netstat -an | findstr :5000")
        print("\n3. Try starting with app.py directly:")
        print("   python app.py")
        
    elif passed < total:
        print("\n⚠️  Some endpoints failed - backend is running but has issues")
        
    else:
        print("\n🎉 All tests passed - backend is working correctly!")

if __name__ == "__main__":
    main()
