{"timestamp": "2025-07-07T19:07:39.167199", "issues": [{"category": "STRUCTURE", "message": "Table 'abandoned_cart' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.687468"}, {"category": "STRUCTURE", "message": "Table 'admin_activity_log' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.707412"}, {"category": "STRUCTURE", "message": "Table 'admin_user' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.719383"}, {"category": "STRUCTURE", "message": "Table 'availability_notification' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.752425"}, {"category": "STRUCTURE", "message": "Table 'banner' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.776329"}, {"category": "STRUCTURE", "message": "Table 'carrier_rates' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.808238"}, {"category": "STRUCTURE", "message": "Table 'cart_item' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.844546"}, {"category": "STRUCTURE", "message": "Table 'channel_inventory' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.874163"}, {"category": "STRUCTURE", "message": "Table 'chat_message' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.886477"}, {"category": "STRUCTURE", "message": "Table 'chat_session' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.900333"}, {"category": "STRUCTURE", "message": "Table 'community_insight' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.917016"}, {"category": "STRUCTURE", "message": "Table 'community_post' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.924894"}, {"category": "STRUCTURE", "message": "Table 'community_stats' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.934797"}, {"category": "STRUCTURE", "message": "Table 'content_page' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.942774"}, {"category": "STRUCTURE", "message": "Table 'cookie_audit_log' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:36.971891"}, {"category": "STRUCTURE", "message": "Table 'cookie_consent' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.020761"}, {"category": "STRUCTURE", "message": "Table 'cookie_consent_history' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.058659"}, {"category": "STRUCTURE", "message": "Table 'coupon' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.094563"}, {"category": "STRUCTURE", "message": "Table 'coupon_usage' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.127475"}, {"category": "STRUCTURE", "message": "Table 'data_export_request' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.159567"}, {"category": "STRUCTURE", "message": "Table 'email_notification' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.190960"}, {"category": "STRUCTURE", "message": "Table 'fulfillment_rules' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.217147"}, {"category": "STRUCTURE", "message": "Table 'guest_session' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.221143"}, {"category": "STRUCTURE", "message": "Table 'hashtag' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.224167"}, {"category": "STRUCTURE", "message": "Table 'inventory_conflict' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.256942"}, {"category": "STRUCTURE", "message": "Table 'inventory_log' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.285848"}, {"category": "STRUCTURE", "message": "Table 'inventory_sync_log' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.311185"}, {"category": "STRUCTURE", "message": "Table 'invoice' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.341528"}, {"category": "STRUCTURE", "message": "Table 'newsletter_subscription' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.371154"}, {"category": "STRUCTURE", "message": "Table 'o_auth_provider' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.375880"}, {"category": "STRUCTURE", "message": "Table 'order' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.416632"}, {"category": "STRUCTURE", "message": "Table 'order_item' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.440342"}, {"category": "STRUCTURE", "message": "Table 'orders' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.470665"}, {"category": "STRUCTURE", "message": "Table 'payment_gateway' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.474658"}, {"category": "STRUCTURE", "message": "Table 'payment_method' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.502034"}, {"category": "STRUCTURE", "message": "Table 'payment_transaction' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.525494"}, {"category": "STRUCTURE", "message": "Table 'post_comment' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.553399"}, {"category": "STRUCTURE", "message": "Table 'post_hashtag' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.560464"}, {"category": "STRUCTURE", "message": "Table 'post_like' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.587946"}, {"category": "STRUCTURE", "message": "Table 'price_history' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.611885"}, {"category": "STRUCTURE", "message": "Table 'product' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.658639"}, {"category": "STRUCTURE", "message": "Table 'product_comparison' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.684700"}, {"category": "STRUCTURE", "message": "Table 'product_image' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.706523"}, {"category": "STRUCTURE", "message": "Table 'product_review' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.733117"}, {"category": "STRUCTURE", "message": "Table 'product_variant' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.755332"}, {"category": "STRUCTURE", "message": "Table 'products' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.766305"}, {"category": "STRUCTURE", "message": "Table 'recently_viewed' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.789880"}, {"category": "STRUCTURE", "message": "Table 'refund' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.821064"}, {"category": "STRUCTURE", "message": "Table 'return_shipment' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.847330"}, {"category": "STRUCTURE", "message": "Table 'rma_approval' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.871284"}, {"category": "STRUCTURE", "message": "Table 'rma_configuration' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.877948"}, {"category": "STRUCTURE", "message": "Table 'rma_document' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.905256"}, {"category": "STRUCTURE", "message": "Table 'rma_item' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.934553"}, {"category": "STRUCTURE", "message": "Table 'rma_request' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.942879"}, {"category": "STRUCTURE", "message": "Table 'rma_rule' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.967468"}, {"category": "STRUCTURE", "message": "Table 'rma_stats' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:37.989035"}, {"category": "STRUCTURE", "message": "Table 'rma_timeline' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.017517"}, {"category": "STRUCTURE", "message": "Table 'sales' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.039857"}, {"category": "STRUCTURE", "message": "Table 'sales_channel' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.049215"}, {"category": "STRUCTURE", "message": "Table 'saved_cart' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.071814"}, {"category": "STRUCTURE", "message": "Table 'search_analytics' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.075996"}, {"category": "STRUCTURE", "message": "Table 'search_clicks' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.084192"}, {"category": "STRUCTURE", "message": "Table 'search_conversions' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.089224"}, {"category": "STRUCTURE", "message": "Table 'seller' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.119299"}, {"category": "STRUCTURE", "message": "Table 'seller_commission' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.142525"}, {"category": "STRUCTURE", "message": "Table 'seller_payout' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.169892"}, {"category": "STRUCTURE", "message": "Table 'seller_store' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.191896"}, {"category": "STRUCTURE", "message": "Table 'sellers' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.203537"}, {"category": "STRUCTURE", "message": "Table 'shipments' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.237064"}, {"category": "STRUCTURE", "message": "Table 'shipping_carriers' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.240912"}, {"category": "STRUCTURE", "message": "Table 'shipping_method' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.268786"}, {"category": "STRUCTURE", "message": "Table 'shipping_zone' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.290948"}, {"category": "STRUCTURE", "message": "Table 'support_attachment' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.318581"}, {"category": "STRUCTURE", "message": "Table 'support_message' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.340850"}, {"category": "STRUCTURE", "message": "Table 'support_ticket' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.371463"}, {"category": "STRUCTURE", "message": "Table 'sync_queue' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.401000"}, {"category": "STRUCTURE", "message": "Table 'tax_rate' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.421973"}, {"category": "STRUCTURE", "message": "Table 'test_table' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.442780"}, {"category": "STRUCTURE", "message": "Table 'tracking_events' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.472197"}, {"category": "STRUCTURE", "message": "Table 'user' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.502447"}, {"category": "STRUCTURE", "message": "Table 'user_address' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.524807"}, {"category": "STRUCTURE", "message": "Table 'user_behavior_profiles' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.552168"}, {"category": "STRUCTURE", "message": "Table 'user_interaction_logs' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.559155"}, {"category": "STRUCTURE", "message": "Table 'user_o_auth' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.586221"}, {"category": "STRUCTURE", "message": "Table 'user_sessions' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.609010"}, {"category": "STRUCTURE", "message": "Table 'users' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.618325"}, {"category": "STRUCTURE", "message": "Table 'visual_search_analytics' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.641473"}, {"category": "STRUCTURE", "message": "Table 'wishlist' has no primary key", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.668902"}, {"category": "DATA_INTEGRITY", "message": "Error checking Products without Sellers: (mysql.connector.errors.ProgrammingError) 1054 (42S22): Unknown column 'p.seller_id' in 'where clause'\n[SQL: \n                        SELECT COUNT(*) FROM product p \n                        LEFT JOIN seller s ON p.seller_id = s.id \n                        WHERE s.id IS NULL AND p.seller_id IS NOT NULL\n                    ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "severity": "ERROR", "timestamp": "2025-07-07T19:07:38.675889"}], "warnings": [{"category": "STRUCTURE", "message": "Potential duplicate tables found: {'order': ['order', 'orders'], 'product': ['product', 'products'], 'seller': ['seller', 'sellers'], 'user': ['user', 'users']}", "severity": "WARNING", "timestamp": "2025-07-07T19:07:36.619494"}, {"category": "DATA_INTEGRITY", "message": "Orphaned Order Items: 1 issues found", "severity": "WARNING", "timestamp": "2025-07-07T19:07:38.674891"}, {"category": "CONSTRAINTS", "message": "Missing FK constraint: order_item.order_id -> orders", "severity": "WARNING", "timestamp": "2025-07-07T19:07:39.134410"}], "recommendations": []}