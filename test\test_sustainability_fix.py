#!/usr/bin/env python3
"""
Test Sustainability API Fix
===========================

Simple test to verify the database context fix is working.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_sustainability_metrics_with_context():
    """Test sustainability metrics with proper app context"""
    print("🧪 Testing Sustainability Metrics with Test Client")
    print("=" * 55)

    try:
        from app import app

        # Test with Flask test client (provides request context)
        with app.test_client() as client:
            print("✅ Test client established")

            # Make a request to the metrics endpoint
            response = client.get('/api/sustainability/metrics')

            print(f"✅ Request completed")
            print(f"   Status Code: {response.status_code}")

            if response.status_code == 200:
                print("✅ SUCCESS: Sustainability metrics working!")
                response_data = response.get_json()
                print(f"   Response keys: {list(response_data.keys())}")
                if 'data' in response_data:
                    metrics = response_data['data']
                    print(f"   Metrics keys: {list(metrics.keys())}")
                    print(f"   CO2 saved: {metrics.get('co2_saved_kg', 0)} kg")
                    print(f"   Trees equivalent: {metrics.get('trees_planted_equivalent', 0)}")
                    print(f"   Total orders: {metrics.get('total_orders', 0)}")
            else:
                print(f"⚠️  Request returned error: {response.get_json()}")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_green_heroes_with_context():
    """Test green heroes with proper app context"""
    print("\n🏆 Testing Green Heroes with Test Client")
    print("=" * 45)

    try:
        from app import app

        # Test with Flask test client (provides request context)
        with app.test_client() as client:
            print("✅ Test client established")

            # Make a request to the green heroes endpoint
            response = client.get('/api/sustainability/green-heroes')

            print(f"✅ Request completed")
            print(f"   Status Code: {response.status_code}")

            if response.status_code == 200:
                print("✅ SUCCESS: Green heroes working!")
                response_data = response.get_json()
                print(f"   Response keys: {list(response_data.keys())}")
                if 'data' in response_data:
                    heroes = response_data['data']
                    print(f"   Heroes count: {len(heroes)}")
                    if heroes:
                        print(f"   First hero: {heroes[0].get('name', 'Unknown')}")
            else:
                print(f"⚠️  Request returned error: {response.get_json()}")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sustainability_goals_with_context():
    """Test sustainability goals with proper app context"""
    print("\n🎯 Testing Sustainability Goals with Test Client")
    print("=" * 50)

    try:
        from app import app

        # Test with Flask test client (provides request context)
        with app.test_client() as client:
            print("✅ Test client established")

            # Make a request to the goals endpoint
            response = client.get('/api/sustainability/goals')

            print(f"✅ Request completed")
            print(f"   Status Code: {response.status_code}")

            if response.status_code == 200:
                print("✅ SUCCESS: Sustainability goals working!")
                response_data = response.get_json()
                print(f"   Response keys: {list(response_data.keys())}")
                if 'data' in response_data:
                    goals = response_data['data']
                    print(f"   Goals count: {len(goals)}")
                    for goal in goals:
                        print(f"   • {goal.get('title', 'Unknown')}: {goal.get('progress', 0)}%")
            else:
                print(f"⚠️  Request returned error: {response.get_json()}")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all sustainability fix tests"""
    print("🚀 Sustainability API Fix Verification")
    print("=" * 45)
    print()
    
    tests = [
        test_sustainability_metrics_with_context,
        test_green_heroes_with_context,
        test_sustainability_goals_with_context
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n📊 Fix Verification Results")
    print("=" * 35)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All fixes working! Sustainability API is now functional.")
        print("\n✅ SUSTAINABILITY API STATUS: FIXED AND OPERATIONAL")
    else:
        print("⚠️  Some fixes still need work.")
        print("\n⚠️ SUSTAINABILITY API STATUS: PARTIALLY FIXED")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
