#!/usr/bin/env python3
"""
Add Sample Community Data via API
=================================

Uses the existing community API endpoints to add sample data:
- Community posts via /api/community_posts
- Product reviews via existing review endpoints

Author: Allora Development Team
Date: 2025-07-11
"""

import requests
import json
import time
from datetime import datetime, timedelta

BASE_URL = "http://localhost:5000"

def create_sample_posts():
    """Create sample community posts using the API"""
    print("📝 Creating sample community posts...")
    
    # Sample posts with sustainability themes
    sample_posts = [
        {
            'content': 'Just switched to these amazing bamboo water bottles! 🌱 The quality is incredible and I love knowing I\'m reducing plastic waste. #sustainability #zerowaste #ecofriendly',
            'post_type': 'photo',
            'image_url': 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=400&h=400&fit=crop',
            'hashtags': ['sustainability', 'zerowaste', 'ecofriendly', 'bamboo']
        },
        {
            'content': 'My journey to zero waste started 6 months ago. Here\'s what I\'ve learned and the products that made the biggest difference! 🌍 #zerowaste #sustainability #greenjourney',
            'post_type': 'text',
            'hashtags': ['zerowaste', 'sustainability', 'greenjourney', 'eco']
        },
        {
            'content': 'Review: This organic skincare set is absolutely amazing! Not only does it make my skin glow, but the packaging is 100% recyclable. Worth every penny! ⭐⭐⭐⭐⭐ #organicskincare #sustainable #beauty',
            'post_type': 'review',
            'image_url': 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=400&fit=crop',
            'hashtags': ['organicskincare', 'sustainable', 'beauty', 'ecofriendly']
        },
        {
            'content': 'Before and after: My home office transformation using only sustainable furniture! The wooden desk is made from reclaimed wood and it\'s absolutely beautiful 🪵✨ #sustainablehome #ecofurniture #green',
            'post_type': 'photo',
            'image_url': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
            'hashtags': ['sustainablehome', 'ecofurniture', 'green', 'sustainable']
        },
        {
            'content': 'Coffee lovers! ☕ This fair trade coffee is not only delicious but supports sustainable farming practices. Every cup makes a difference! #fairtrade #sustainablecoffee #ethical',
            'post_type': 'photo',
            'image_url': 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400&h=400&fit=crop',
            'hashtags': ['fairtrade', 'sustainablecoffee', 'ethical', 'coffee']
        }
    ]
    
    # Note: This would require authentication token
    # For now, we'll just show what the API calls would look like
    
    print("📋 Sample community posts ready to be created:")
    for i, post in enumerate(sample_posts, 1):
        print(f"  {i}. {post['post_type'].title()} post: {post['content'][:50]}...")
        print(f"     Hashtags: {', '.join(post['hashtags'])}")
        if post.get('image_url'):
            print(f"     Image: {post['image_url']}")
        print()
    
    print("ℹ️  To create these posts, users need to:")
    print("   1. Register/login to get authentication token")
    print("   2. Use the /api/community_posts POST endpoint")
    print("   3. Include the token in Authorization header")
    
    return True

def create_sample_reviews():
    """Create sample product reviews"""
    print("⭐ Creating sample product reviews...")
    
    sample_reviews = [
        {
            'product_id': 1,
            'rating': 5,
            'title': 'Absolutely love this sustainable product!',
            'comment': 'This product exceeded my expectations! The quality is outstanding and I love that it\'s made from sustainable materials. The packaging was minimal and recyclable. Highly recommend to anyone looking to make more eco-friendly choices!'
        },
        {
            'product_id': 2,
            'rating': 5,
            'title': 'Great quality and eco-friendly!',
            'comment': 'I\'ve been using this for a few weeks now and I\'m impressed with both the quality and the environmental impact. It works exactly as described and feels good knowing I\'m supporting sustainable practices.'
        },
        {
            'product_id': 3,
            'rating': 4,
            'title': 'Good product, fast shipping',
            'comment': 'Really happy with this purchase. The product is well-made and the sustainability aspect is a huge plus. Shipping was fast and packaging was minimal. Would definitely buy again!'
        }
    ]
    
    print("📋 Sample product reviews ready to be created:")
    for i, review in enumerate(sample_reviews, 1):
        print(f"  {i}. Product {review['product_id']}: {review['rating']} stars")
        print(f"     Title: {review['title']}")
        print(f"     Comment: {review['comment'][:50]}...")
        print()
    
    print("ℹ️  To create these reviews, users need to:")
    print("   1. Register/login to get authentication token")
    print("   2. Use the product review endpoints")
    print("   3. Include the token in Authorization header")
    
    return True

def test_community_apis():
    """Test the community highlight APIs"""
    print("🧪 Testing Community Highlights APIs...")
    
    endpoints_to_test = [
        '/api/community-highlights/recent-posts?limit=3',
        '/api/community-highlights/sustainability-stories?limit=3',
        '/api/community-highlights/featured-brands?limit=3',
        '/api/community-highlights/recent-reviews?limit=3'
    ]
    
    for endpoint in endpoints_to_test:
        try:
            print(f"Testing: {endpoint}")
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                count = len(data.get('data', []))
                print(f"  ✅ Success: {count} items returned")
            else:
                print(f"  ⚠️  Status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Error: {e}")
        
        time.sleep(0.5)  # Small delay between requests
    
    return True

def show_community_setup_instructions():
    """Show instructions for setting up community data"""
    print("\n" + "=" * 60)
    print("🎯 COMMUNITY HIGHLIGHTS SETUP INSTRUCTIONS")
    print("=" * 60)
    
    print("\n📝 To populate Community Highlights with real data:")
    
    print("\n1. 👥 CREATE COMMUNITY POSTS:")
    print("   • Visit: http://localhost:3000/community")
    print("   • Register/login as a user")
    print("   • Create posts with sustainability themes")
    print("   • Use hashtags: #sustainability #eco #green #zerowaste")
    print("   • Add photos to make posts more engaging")
    
    print("\n2. ⭐ ADD PRODUCT REVIEWS:")
    print("   • Visit product pages: http://localhost:3000/product/[id]")
    print("   • Login and write detailed reviews")
    print("   • Focus on sustainability aspects")
    print("   • Rate products 4-5 stars for quality content")
    
    print("\n3. 🏪 FEATURED BRANDS:")
    print("   • Brands are automatically featured based on:")
    print("     - High sustainability scores (80+)")
    print("     - Good customer ratings")
    print("     - Multiple products in catalog")
    
    print("\n4. 🌱 SUSTAINABILITY STORIES:")
    print("   • Create posts with sustainability hashtags")
    print("   • Share eco-friendly journeys and tips")
    print("   • Include before/after photos")
    print("   • Engage with other users' posts")
    
    print("\n🚀 QUICK START:")
    print("   1. Go to http://localhost:3000/community")
    print("   2. Create an account or login")
    print("   3. Make your first post about sustainability")
    print("   4. Visit http://localhost:3000 to see it in Community Highlights!")
    
    print("\n💡 TIP: The more community activity, the richer the highlights!")

def main():
    print("👥 COMMUNITY HIGHLIGHTS DATA SETUP")
    print("=" * 60)
    
    # Test APIs first
    if not test_community_apis():
        print("❌ API tests failed")
        return False
    
    # Show sample data that could be created
    if not create_sample_posts():
        print("❌ Failed to prepare sample posts")
        return False
    
    if not create_sample_reviews():
        print("❌ Failed to prepare sample reviews")
        return False
    
    # Show setup instructions
    show_community_setup_instructions()
    
    print("\n" + "=" * 60)
    print("✅ COMMUNITY HIGHLIGHTS READY!")
    print("=" * 60)
    
    print("\n🌐 Visit http://localhost:3000 to see Community Highlights section")
    print("📱 The section will show a 'Community is Growing' message until data is added")
    print("🎯 Follow the instructions above to populate with real community content")
    
    return True

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
