#!/usr/bin/env python3
"""
Live Webhook Test Script
========================

Tests webhook endpoints with the running server.
"""

import requests
import json

def test_webhook_test_endpoint():
    """Test the webhook test endpoint"""
    print("🔍 Testing Webhook Test Endpoint...")
    
    url = "http://localhost:5000/api/webhooks/test"
    
    # Test data
    test_data = {
        "carrier": "blue_dart",
        "tracking_number": "TEST123456",
        "status": "in_transit",
        "timestamp": "2025-07-13T10:00:00Z",
        "event_type": "status_update"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=10)
        print(f"✅ Status Code: {response.status_code}")
        print(f"✅ Response: {response.text}")
        
        if response.status_code == 200:
            print("🎉 Webhook test endpoint working perfectly!")
            return True
        else:
            print(f"⚠️  Webhook returned status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Server not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error testing webhook: {e}")
        return False

def test_webhook_with_invalid_data():
    """Test webhook with invalid data"""
    print("\n🔍 Testing Webhook with Invalid Data...")
    
    url = "http://localhost:5000/api/webhooks/test"
    
    # Invalid test data (missing required fields)
    invalid_data = {
        "invalid": "data"
    }
    
    try:
        response = requests.post(url, json=invalid_data, timeout=10)
        print(f"✅ Status Code: {response.status_code}")
        print(f"✅ Response: {response.text}")
        
        if response.status_code == 400:
            print("🎉 Webhook properly validates data!")
            return True
        else:
            print(f"⚠️  Expected 400, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing webhook: {e}")
        return False

def test_server_health():
    """Test if server is responding"""
    print("\n🔍 Testing Server Health...")
    
    try:
        response = requests.get("http://localhost:5000/", timeout=5)
        print(f"✅ Server responding with status: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server not accessible")
        return False
    except Exception as e:
        print(f"❌ Error checking server: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Live Webhook Testing")
    print("=" * 40)
    
    # Test server health first
    if not test_server_health():
        print("\n❌ Server is not running. Please start it with:")
        print("   python run_with_waitress.py")
        return
    
    # Test webhook endpoints
    results = []
    results.append(test_webhook_test_endpoint())
    results.append(test_webhook_with_invalid_data())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All webhook tests passed! Webhooks are working perfectly!")
    elif passed > 0:
        print("✅ Some webhook functionality working")
    else:
        print("❌ Webhook functionality needs attention")

if __name__ == '__main__':
    main()
