#!/usr/bin/env python3
"""
Data Inspection Script for Allora E-commerce Platform
=====================================================

This script checks what data currently exists in your database and files
without making any changes. Use this to see what will be cleaned.

Usage:
    python check_existing_data.py
"""

import os
import sys
import glob
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

class DataInspector:
    """Class for inspecting existing data without making changes"""
    
    def __init__(self):
        self.total_records = 0
        self.total_files = 0
        
    def check_database_data(self):
        """Check what data exists in the database"""
        print("🗄️  DATABASE DATA INSPECTION")
        print("=" * 50)
        
        try:
            # Import Flask app and models
            from app import app, db
            from app import (
                User, Product, Order, OrderItem, Seller, AdminUser,
                ProductReview, ProductImage, ProductVariant, PriceHistory,
                UserAddress, PaymentMethod, Wishlist, CartItem, UserInteractionLog, UserBehaviorProfile,
                CommunityPost, PostComment, PostLike, Hashtag, PostHashtag, CommunityStats,
                Coupon, CouponUsage, TaxRate, ShippingZone, ShippingMethod,
                Sales, SearchAnalytics, VisualSearchAnalytics, NewsletterSubscription,
                ShippingCarrier, Shipment, TrackingEvent, FulfillmentRule, CarrierRate,
                RMARequest, RMAItem, RMATimeline, RMADocument, RMAApproval, RMARule, RMAConfiguration,
                SupportTicket, SupportMessage, SupportAttachment,
                PaymentGateway, PaymentTransaction, Invoice, Refund,
                SellerStore, SellerCommission, SellerPayout,
                ContentPage, Banner, RecentlyViewed, AvailabilityNotification,
                InventoryLog, SalesChannel, ChannelInventory, InventorySyncLog,
                OAuthProvider, UserOAuth,
                EmailNotification, GuestSession, SavedCart, AbandonedCart,
                CookieConsent, DataExportRequest
            )
            
            with app.app_context():
                # Tables to check with their display names
                tables_to_check = [
                    (User, "Users"),
                    (Product, "Products"),
                    (Order, "Orders"),
                    (OrderItem, "Order Items"),
                    (Seller, "Sellers"),
                    (AdminUser, "Admin Users"),
                    (ProductReview, "Product Reviews"),
                    (ProductImage, "Product Images"),
                    (ProductVariant, "Product Variants"),
                    (CommunityPost, "Community Posts"),
                    (PostComment, "Post Comments"),
                    (PostLike, "Post Likes"),
                    (Hashtag, "Hashtags"),
                    (CartItem, "Cart Items"),
                    (Wishlist, "Wishlist Items"),
                    (UserInteractionLog, "User Interactions"),
                    (PaymentGateway, "Payment Gateways"),
                    (ShippingCarrier, "Shipping Carriers"),
                    (Coupon, "Coupons"),
                    (ContentPage, "Content Pages"),
                    (Banner, "Banners"),
                    (NewsletterSubscription, "Newsletter Subscriptions"),
                    (SearchAnalytics, "Search Analytics"),
                    (SupportTicket, "Support Tickets"),
                    (RMARequest, "RMA Requests")
                ]
                
                print("📊 Record counts by table:")
                print()
                
                has_data = False
                
                for table_model, table_name in tables_to_check:
                    try:
                        count = db.session.query(table_model).count()
                        if count > 0:
                            print(f"  📋 {table_name:<25} {count:>8} records")
                            self.total_records += count
                            has_data = True
                        else:
                            print(f"  ⚪ {table_name:<25} {'empty':>8}")
                    except Exception as e:
                        print(f"  ❌ {table_name:<25} {'error':>8} - {str(e)[:30]}...")
                
                print()
                if has_data:
                    print(f"📈 Total database records: {self.total_records:,}")
                    print("⚠️  Database contains data that will be cleared")
                else:
                    print("✅ Database appears to be empty")
                
                return has_data
                
        except Exception as e:
            print(f"❌ Could not connect to database: {e}")
            print("💡 Make sure your database is running and .env is configured")
            return False

    def check_backend_files(self):
        """Check what files exist in the backend"""
        print("\n🔧 BACKEND FILES INSPECTION")
        print("=" * 50)
        
        backend_path = os.path.dirname(os.path.abspath(__file__))
        
        # Files and patterns to check
        file_patterns = [
            ("logs/*.log", "Log files"),
            ("logs/*.log.*", "Rotated log files"),
            ("__pycache__", "Python cache directories"),
            ("*.pyc", "Python compiled files"),
            ("models/**/*.pkl", "ML model files"),
            ("models/**/*.joblib", "ML model files"),
            ("models/**/*.h5", "TensorFlow model files"),
            ("*.tmp", "Temporary files"),
            ("*.temp", "Temporary files"),
            ("test_*.py", "Test files"),
            ("*_test.py", "Test files")
        ]
        
        print("📁 Files that would be cleaned:")
        print()
        
        has_files = False
        
        for pattern, description in file_patterns:
            full_pattern = os.path.join(backend_path, pattern)
            
            if "**" in pattern:
                matches = glob.glob(full_pattern, recursive=True)
            else:
                matches = glob.glob(full_pattern)
            
            if matches:
                print(f"  📄 {description:<25} {len(matches):>4} items")
                for match in matches[:3]:  # Show first 3 matches
                    rel_path = os.path.relpath(match, backend_path)
                    print(f"     └─ {rel_path}")
                if len(matches) > 3:
                    print(f"     └─ ... and {len(matches) - 3} more")
                self.total_files += len(matches)
                has_files = True
            else:
                print(f"  ⚪ {description:<25} {'none':>4}")
        
        print()
        if has_files:
            print(f"📈 Total backend files: {self.total_files}")
            print("⚠️  Backend contains files that will be cleaned")
        else:
            print("✅ Backend appears to be clean")
        
        return has_files

    def check_frontend_files(self):
        """Check what files exist in the frontend"""
        print("\n🎨 FRONTEND FILES INSPECTION")
        print("=" * 50)
        
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "frontend")
        
        if not os.path.exists(frontend_path):
            print("ℹ️  Frontend directory not found")
            return False
        
        # Directories and files to check
        items_to_check = [
            ("build", "Build directory"),
            ("dist", "Distribution directory"),
            ("node_modules/.cache", "Node.js cache"),
            (".next", "Next.js cache"),
            ("coverage", "Test coverage"),
        ]
        
        # File patterns
        file_patterns = [
            ("*.log", "Log files"),
            ("npm-debug.log*", "NPM debug logs"),
            ("yarn-debug.log*", "Yarn debug logs"),
            ("yarn-error.log*", "Yarn error logs")
        ]
        
        print("📁 Items that would be cleaned:")
        print()
        
        has_items = False
        frontend_files = 0
        
        # Check directories
        for item, description in items_to_check:
            item_path = os.path.join(frontend_path, item)
            if os.path.exists(item_path):
                if os.path.isdir(item_path):
                    # Count files in directory
                    file_count = sum(len(files) for _, _, files in os.walk(item_path))
                    print(f"  📁 {description:<25} {file_count:>8} files")
                    frontend_files += file_count
                else:
                    print(f"  📄 {description:<25} {'1 file':>8}")
                    frontend_files += 1
                has_items = True
            else:
                print(f"  ⚪ {description:<25} {'none':>8}")
        
        # Check file patterns
        for pattern, description in file_patterns:
            matches = glob.glob(os.path.join(frontend_path, pattern))
            if matches:
                print(f"  📄 {description:<25} {len(matches):>4} files")
                frontend_files += len(matches)
                has_items = True
            else:
                print(f"  ⚪ {description:<25} {'none':>4}")
        
        print()
        if has_items:
            print(f"📈 Total frontend files: {frontend_files}")
            print("⚠️  Frontend contains files that will be cleaned")
        else:
            print("✅ Frontend appears to be clean")
        
        return has_items

    def run_inspection(self):
        """Run the complete inspection"""
        print("🔍 ALLORA PROJECT DATA INSPECTION")
        print("=" * 50)
        print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("ℹ️  This inspection does not modify any data")
        print()
        
        # Check all areas
        has_db_data = self.check_database_data()
        has_backend_files = self.check_backend_files()
        has_frontend_files = self.check_frontend_files()
        
        # Summary
        print("\n" + "=" * 50)
        print("📋 INSPECTION SUMMARY")
        print("=" * 50)
        
        if has_db_data:
            print(f"🗄️  Database: {self.total_records:,} records found")
        else:
            print("🗄️  Database: Empty or inaccessible")
        
        if has_backend_files:
            print(f"🔧 Backend: {self.total_files} files found")
        else:
            print("🔧 Backend: Clean")
        
        if has_frontend_files:
            print("🎨 Frontend: Files found")
        else:
            print("🎨 Frontend: Clean")
        
        print()
        
        if has_db_data or has_backend_files or has_frontend_files:
            print("⚠️  Your project contains data/files that can be cleaned")
            print()
            print("🧹 To clean your project:")
            print("   python run_cleanup.py      # Interactive cleanup")
            print("   python clear_all_data.py   # Direct cleanup")
            print()
            print("📖 For more information:")
            print("   See CLEANUP_GUIDE.md")
        else:
            print("✅ Your project appears to be clean!")
            print()
            print("🌱 Ready for seeding:")
            print("   python run_seeder.py       # Interactive seeding")
            print("   python seed_database.py    # Direct seeding")
        
        print(f"\n🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """Main function"""
    inspector = DataInspector()
    inspector.run_inspection()

if __name__ == '__main__':
    main()
