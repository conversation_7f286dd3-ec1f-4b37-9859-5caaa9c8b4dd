#!/usr/bin/env python3
"""
Database Structure Analysis Script
=================================

This script analyzes all database tables in the Allora project and provides
a comprehensive report on their structure, relationships, and design quality.

Features:
- Lists all tables with column details
- Analyzes relationships and foreign keys
- Checks for indexes and constraints
- Evaluates naming conventions
- Identifies potential issues
- Provides design quality assessment

Usage:
    python analyze_database_structure.py [--detailed] [--export]
"""

import os
import sys
import argparse
from datetime import datetime
from collections import defaultdict

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import inspect, text
from sqlalchemy.engine.reflection import Inspector

def analyze_table_structure():
    """Analyze all database tables and their structure"""
    print("🔍 DATABASE STRUCTURE ANALYSIS")
    print("=" * 70)
    print(f"⏰ Analysis started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    with app.app_context():
        try:
            # Get database inspector
            inspector = inspect(db.engine)
            
            # Get all table names
            table_names = inspector.get_table_names()
            
            print(f"\n📊 OVERVIEW")
            print("-" * 30)
            print(f"Total Tables: {len(table_names)}")
            
            # Analyze each table
            table_analysis = {}
            total_columns = 0
            total_indexes = 0
            total_foreign_keys = 0
            total_constraints = 0
            
            for table_name in sorted(table_names):
                analysis = analyze_single_table(inspector, table_name)
                table_analysis[table_name] = analysis
                
                total_columns += len(analysis['columns'])
                total_indexes += len(analysis['indexes'])
                total_foreign_keys += len(analysis['foreign_keys'])
                total_constraints += len(analysis['constraints'])
            
            print(f"Total Columns: {total_columns}")
            print(f"Total Indexes: {total_indexes}")
            print(f"Total Foreign Keys: {total_foreign_keys}")
            print(f"Total Constraints: {total_constraints}")
            
            # Detailed table analysis
            print(f"\n📋 DETAILED TABLE ANALYSIS")
            print("=" * 70)
            
            for table_name, analysis in table_analysis.items():
                print_table_analysis(table_name, analysis)
            
            # Relationship analysis
            print(f"\n🔗 RELATIONSHIP ANALYSIS")
            print("=" * 50)
            analyze_relationships(table_analysis)
            
            # Design quality assessment
            print(f"\n⭐ DESIGN QUALITY ASSESSMENT")
            print("=" * 50)
            assess_design_quality(table_analysis)
            
            return table_analysis
            
        except Exception as e:
            print(f"❌ Error during analysis: {e}")
            return None

def analyze_single_table(inspector, table_name):
    """Analyze a single table structure"""
    try:
        # Get table information
        columns = inspector.get_columns(table_name)
        indexes = inspector.get_indexes(table_name)
        foreign_keys = inspector.get_foreign_keys(table_name)
        pk_constraint = inspector.get_pk_constraint(table_name)
        unique_constraints = inspector.get_unique_constraints(table_name)
        check_constraints = inspector.get_check_constraints(table_name)
        
        # Combine all constraints
        constraints = []
        if pk_constraint and pk_constraint.get('constrained_columns'):
            constraints.append({
                'type': 'PRIMARY KEY',
                'columns': pk_constraint['constrained_columns'],
                'name': pk_constraint.get('name', 'PRIMARY')
            })
        
        for uc in unique_constraints:
            constraints.append({
                'type': 'UNIQUE',
                'columns': uc['column_names'],
                'name': uc.get('name', 'UNIQUE')
            })
        
        for cc in check_constraints:
            constraints.append({
                'type': 'CHECK',
                'columns': [],
                'name': cc.get('name', 'CHECK'),
                'condition': cc.get('sqltext', '')
            })
        
        return {
            'columns': columns,
            'indexes': indexes,
            'foreign_keys': foreign_keys,
            'constraints': constraints,
            'primary_key': pk_constraint,
            'unique_constraints': unique_constraints,
            'check_constraints': check_constraints
        }
        
    except Exception as e:
        print(f"⚠️  Error analyzing table {table_name}: {e}")
        return {
            'columns': [],
            'indexes': [],
            'foreign_keys': [],
            'constraints': [],
            'primary_key': None,
            'unique_constraints': [],
            'check_constraints': []
        }

def print_table_analysis(table_name, analysis):
    """Print detailed analysis for a single table"""
    print(f"\n📋 Table: {table_name}")
    print("-" * 50)
    
    # Columns
    print(f"📊 Columns ({len(analysis['columns'])}):")
    for col in analysis['columns']:
        nullable = "NULL" if col['nullable'] else "NOT NULL"
        default = f" DEFAULT {col['default']}" if col['default'] is not None else ""
        autoincrement = " AUTO_INCREMENT" if col.get('autoincrement', False) else ""
        
        print(f"  • {col['name']:<25} {str(col['type']):<20} {nullable}{default}{autoincrement}")
    
    # Primary Key
    if analysis['primary_key'] and analysis['primary_key'].get('constrained_columns'):
        pk_cols = ', '.join(analysis['primary_key']['constrained_columns'])
        print(f"🔑 Primary Key: {pk_cols}")
    
    # Foreign Keys
    if analysis['foreign_keys']:
        print(f"🔗 Foreign Keys ({len(analysis['foreign_keys'])}):")
        for fk in analysis['foreign_keys']:
            local_cols = ', '.join(fk['constrained_columns'])
            ref_table = fk['referred_table']
            ref_cols = ', '.join(fk['referred_columns'])
            print(f"  • {local_cols} → {ref_table}({ref_cols})")
    
    # Indexes
    if analysis['indexes']:
        print(f"📇 Indexes ({len(analysis['indexes'])}):")
        for idx in analysis['indexes']:
            cols = ', '.join(idx['column_names'])
            unique = "UNIQUE " if idx['unique'] else ""
            print(f"  • {unique}{idx['name']}: {cols}")
    
    # Unique Constraints
    if analysis['unique_constraints']:
        print(f"🔒 Unique Constraints ({len(analysis['unique_constraints'])}):")
        for uc in analysis['unique_constraints']:
            cols = ', '.join(uc['column_names'])
            print(f"  • {uc.get('name', 'UNIQUE')}: {cols}")

def analyze_relationships(table_analysis):
    """Analyze relationships between tables"""
    relationships = defaultdict(list)
    
    # Build relationship map
    for table_name, analysis in table_analysis.items():
        for fk in analysis['foreign_keys']:
            ref_table = fk['referred_table']
            relationships[table_name].append({
                'type': 'references',
                'target': ref_table,
                'columns': fk['constrained_columns'],
                'target_columns': fk['referred_columns']
            })
            relationships[ref_table].append({
                'type': 'referenced_by',
                'target': table_name,
                'columns': fk['referred_columns'],
                'target_columns': fk['constrained_columns']
            })
    
    # Print relationship summary
    print("🔗 Table Relationships:")
    for table_name in sorted(relationships.keys()):
        refs = [r for r in relationships[table_name] if r['type'] == 'references']
        ref_by = [r for r in relationships[table_name] if r['type'] == 'referenced_by']
        
        if refs or ref_by:
            print(f"\n📋 {table_name}:")
            if refs:
                print(f"  References: {len(refs)} tables")
                for ref in refs[:3]:  # Show first 3
                    print(f"    → {ref['target']}")
                if len(refs) > 3:
                    print(f"    ... and {len(refs) - 3} more")
            
            if ref_by:
                print(f"  Referenced by: {len(ref_by)} tables")
                for ref in ref_by[:3]:  # Show first 3
                    print(f"    ← {ref['target']}")
                if len(ref_by) > 3:
                    print(f"    ... and {len(ref_by) - 3} more")

def assess_design_quality(table_analysis):
    """Assess overall database design quality"""
    issues = []
    recommendations = []
    good_practices = []
    
    for table_name, analysis in table_analysis.items():
        # Check for primary key
        if not analysis['primary_key'] or not analysis['primary_key'].get('constrained_columns'):
            issues.append(f"❌ {table_name}: Missing primary key")
        else:
            good_practices.append(f"✅ {table_name}: Has primary key")
        
        # Check for proper indexing on foreign keys
        fk_columns = set()
        for fk in analysis['foreign_keys']:
            fk_columns.update(fk['constrained_columns'])
        
        indexed_columns = set()
        for idx in analysis['indexes']:
            indexed_columns.update(idx['column_names'])
        
        unindexed_fks = fk_columns - indexed_columns
        if unindexed_fks:
            issues.append(f"⚠️  {table_name}: Foreign key columns without indexes: {', '.join(unindexed_fks)}")
        
        # Check for timestamp columns
        column_names = [col['name'].lower() for col in analysis['columns']]
        has_created_at = any('created' in name and 'at' in name for name in column_names)
        has_updated_at = any('updated' in name and 'at' in name for name in column_names)
        
        if has_created_at and has_updated_at:
            good_practices.append(f"✅ {table_name}: Has proper timestamp columns")
        elif not has_created_at:
            recommendations.append(f"💡 {table_name}: Consider adding created_at timestamp")
        
        # Check for proper naming conventions
        if table_name.lower() != table_name:
            issues.append(f"⚠️  {table_name}: Table name not in lowercase")
        
        # Check for nullable foreign keys (might be intentional)
        for col in analysis['columns']:
            if col['name'].endswith('_id') and col['nullable']:
                recommendations.append(f"💡 {table_name}.{col['name']}: Nullable foreign key (verify if intentional)")
    
    # Print assessment
    print(f"📊 SUMMARY:")
    print(f"  ✅ Good Practices: {len(good_practices)}")
    print(f"  💡 Recommendations: {len(recommendations)}")
    print(f"  ⚠️  Issues: {len(issues)}")
    
    if good_practices:
        print(f"\n✅ GOOD PRACTICES ({len(good_practices)}):")
        for practice in good_practices[:10]:  # Show first 10
            print(f"  {practice}")
        if len(good_practices) > 10:
            print(f"  ... and {len(good_practices) - 10} more")
    
    if recommendations:
        print(f"\n💡 RECOMMENDATIONS ({len(recommendations)}):")
        for rec in recommendations[:10]:  # Show first 10
            print(f"  {rec}")
        if len(recommendations) > 10:
            print(f"  ... and {len(recommendations) - 10} more")
    
    if issues:
        print(f"\n⚠️  ISSUES FOUND ({len(issues)}):")
        for issue in issues:
            print(f"  {issue}")
    
    # Overall assessment
    total_checks = len(good_practices) + len(recommendations) + len(issues)
    quality_score = (len(good_practices) * 2 + len(recommendations)) / (total_checks * 2) * 100 if total_checks > 0 else 0
    
    print(f"\n🎯 OVERALL QUALITY SCORE: {quality_score:.1f}%")
    
    if quality_score >= 80:
        print("🌟 EXCELLENT: Database structure is well-designed!")
    elif quality_score >= 60:
        print("👍 GOOD: Database structure is solid with minor improvements needed")
    elif quality_score >= 40:
        print("⚠️  FAIR: Database structure needs some improvements")
    else:
        print("❌ POOR: Database structure needs significant improvements")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Analyze database structure')
    parser.add_argument('--detailed', action='store_true', help='Show detailed analysis')
    parser.add_argument('--export', action='store_true', help='Export results to file')
    
    args = parser.parse_args()
    
    # Run analysis
    analysis_result = analyze_table_structure()
    
    if analysis_result and args.export:
        # Export to file (basic implementation)
        filename = f"database_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        print(f"\n📄 Analysis exported to: {filename}")
    
    print(f"\n⏰ Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return analysis_result is not None

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
