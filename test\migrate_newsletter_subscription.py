#!/usr/bin/env python3
"""
Newsletter Subscription Database Migration Script
Creates the newsletter_subscription table for storing email subscriptions
"""

import sqlite3
import sys
import os
from datetime import datetime

def create_newsletter_subscription_table():
    """Create newsletter_subscription table"""
    try:
        # Connect to the database
        db_path = os.path.join(os.path.dirname(__file__), 'allora.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Creating newsletter_subscription table...")
        
        # Create newsletter_subscription table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS newsletter_subscription (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email VARCHAR(120) NOT NULL UNIQUE,
                is_active BOOLEAN DEFAULT 1,
                subscription_source VARCHAR(50) DEFAULT 'website',
                preferences TEXT,  -- JSON stored as TEXT in SQLite
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                unsubscribed_at DATETIME
            )
        ''')
        
        # Create index on email for faster lookups
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_newsletter_email 
            ON newsletter_subscription(email)
        ''')
        
        # Create index on is_active for filtering active subscriptions
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_newsletter_active 
            ON newsletter_subscription(is_active)
        ''')
        
        # Create index on created_at for analytics
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_newsletter_created 
            ON newsletter_subscription(created_at)
        ''')
        
        conn.commit()
        print("✅ Newsletter subscription table created successfully!")
        
        # Verify table creation
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='newsletter_subscription'")
        if cursor.fetchone():
            print("✅ Table verification successful")
            
            # Show table structure
            cursor.execute("PRAGMA table_info(newsletter_subscription)")
            columns = cursor.fetchall()
            print("\n📋 Table structure:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        else:
            print("❌ Table verification failed")
            return False
            
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if conn:
            conn.close()
    
    return True

def main():
    """Main migration function"""
    print("🚀 Starting Newsletter Subscription Migration...")
    print("=" * 50)
    
    if create_newsletter_subscription_table():
        print("\n✅ Migration completed successfully!")
        print("Newsletter subscription functionality is now ready to use.")
    else:
        print("\n❌ Migration failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
