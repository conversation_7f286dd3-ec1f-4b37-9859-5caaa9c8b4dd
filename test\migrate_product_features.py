#!/usr/bin/env python3
"""
Database migration script for Product Features
Adds new tables and columns for enhanced product functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

def run_migration():
    """Run the database migration for product features"""
    with app.app_context():
        try:
            print("Starting Product Features migration...")
            
            # Add new columns to Product table
            print("Adding new columns to Product table...")
            
            # Check if columns already exist before adding them
            columns_to_add = [
                ("description", "TEXT"),
                ("category", "VARCHAR(50)"),
                ("brand", "VARCHAR(50)"),
                ("sku", "VARCHAR(100)"),
                ("weight", "DECIMAL(10,2)"),
                ("dimensions", "VARCHAR(100)"),
                ("material", "VARCHAR(100)"),
                ("care_instructions", "TEXT"),
                ("average_rating", "DECIMAL(3,2) DEFAULT 0.0"),
                ("total_reviews", "INT DEFAULT 0"),
                ("low_stock_threshold", "INT DEFAULT 10 NOT NULL"),
                ("created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
                ("updated_at", "DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
            ]
            
            for column_name, column_def in columns_to_add:
                try:
                    # Check if column exists
                    result = db.session.execute(text(f"""
                        SELECT COUNT(*) as count 
                        FROM INFORMATION_SCHEMA.COLUMNS 
                        WHERE TABLE_NAME = 'product' 
                        AND COLUMN_NAME = '{column_name}'
                        AND TABLE_SCHEMA = DATABASE()
                    """)).fetchone()
                    
                    if result.count == 0:
                        db.session.execute(text(f"ALTER TABLE product ADD COLUMN {column_name} {column_def}"))
                        print(f"  ✓ Added column: {column_name}")
                    else:
                        print(f"  - Column already exists: {column_name}")
                except Exception as e:
                    print(f"  ✗ Error adding column {column_name}: {str(e)}")
            
            # Create ProductReview table
            print("Creating ProductReview table...")
            try:
                db.session.execute(text("""
                    CREATE TABLE IF NOT EXISTS product_review (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        product_id INT NOT NULL,
                        user_id INT NOT NULL,
                        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                        title VARCHAR(200),
                        comment TEXT,
                        verified_purchase BOOLEAN DEFAULT FALSE,
                        helpful_count INT DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE,
                        FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_user_product_review (user_id, product_id)
                    )
                """))
                print("  ✓ ProductReview table created")
            except Exception as e:
                print(f"  ✗ Error creating ProductReview table: {str(e)}")
            
            # Create ProductImage table
            print("Creating ProductImage table...")
            try:
                db.session.execute(text("""
                    CREATE TABLE IF NOT EXISTS product_image (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        product_id INT NOT NULL,
                        image_url VARCHAR(500) NOT NULL,
                        alt_text VARCHAR(200),
                        is_primary BOOLEAN DEFAULT FALSE,
                        display_order INT DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE
                    )
                """))
                print("  ✓ ProductImage table created")
            except Exception as e:
                print(f"  ✗ Error creating ProductImage table: {str(e)}")
            
            # Create ProductVariant table
            print("Creating ProductVariant table...")
            try:
                db.session.execute(text("""
                    CREATE TABLE IF NOT EXISTS product_variant (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        product_id INT NOT NULL,
                        variant_type VARCHAR(50) NOT NULL,
                        variant_value VARCHAR(100) NOT NULL,
                        price_adjustment DECIMAL(10,2) DEFAULT 0.0,
                        stock_quantity INT DEFAULT 0,
                        sku_suffix VARCHAR(50),
                        is_available BOOLEAN DEFAULT TRUE,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE
                    )
                """))
                print("  ✓ ProductVariant table created")
            except Exception as e:
                print(f"  ✗ Error creating ProductVariant table: {str(e)}")
            
            # Create RecentlyViewed table
            print("Creating RecentlyViewed table...")
            try:
                db.session.execute(text("""
                    CREATE TABLE IF NOT EXISTS recently_viewed (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        product_id INT NOT NULL,
                        viewed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
                        FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_user_product_view (user_id, product_id)
                    )
                """))
                print("  ✓ RecentlyViewed table created")
            except Exception as e:
                print(f"  ✗ Error creating RecentlyViewed table: {str(e)}")
            
            # Create AvailabilityNotification table
            print("Creating AvailabilityNotification table...")
            try:
                db.session.execute(text("""
                    CREATE TABLE IF NOT EXISTS availability_notification (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        product_id INT NOT NULL,
                        variant_id INT,
                        email VARCHAR(255) NOT NULL,
                        is_notified BOOLEAN DEFAULT FALSE,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        notified_at DATETIME,
                        FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
                        FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE,
                        FOREIGN KEY (variant_id) REFERENCES product_variant(id) ON DELETE CASCADE
                    )
                """))
                print("  ✓ AvailabilityNotification table created")
            except Exception as e:
                print(f"  ✗ Error creating AvailabilityNotification table: {str(e)}")
            
            # Create ProductComparison table
            print("Creating ProductComparison table...")
            try:
                db.session.execute(text("""
                    CREATE TABLE IF NOT EXISTS product_comparison (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        comparison_name VARCHAR(200) NOT NULL,
                        product_ids TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
                    )
                """))
                print("  ✓ ProductComparison table created")
            except Exception as e:
                print(f"  ✗ Error creating ProductComparison table: {str(e)}")
            
            # Commit all changes
            db.session.commit()
            print("\n✅ Product Features migration completed successfully!")
            
            # Add some sample data
            print("\nAdding sample product images and variants...")
            add_sample_data()
            
        except Exception as e:
            db.session.rollback()
            print(f"\n❌ Migration failed: {str(e)}")
            raise

def add_sample_data():
    """Add sample product images and variants for existing products"""
    try:
        # Add sample images for first few products
        sample_images = [
            (1, "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400", "Organic Cotton T-Shirt Front View", True, 1),
            (1, "https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=400", "Organic Cotton T-Shirt Back View", False, 2),
            (2, "https://images.unsplash.com/photo-1542272604-787c3835535d?w=400", "Bamboo Water Bottle Main", True, 1),
            (2, "https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=400", "Bamboo Water Bottle Detail", False, 2),
            (3, "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400", "Eco-Friendly Backpack Front", True, 1),
            (3, "https://images.unsplash.com/photo-1581605405669-fcdf81165afa?w=400", "Eco-Friendly Backpack Interior", False, 2),
        ]
        
        for product_id, image_url, alt_text, is_primary, display_order in sample_images:
            try:
                db.session.execute(text("""
                    INSERT IGNORE INTO product_image (product_id, image_url, alt_text, is_primary, display_order)
                    VALUES (:product_id, :image_url, :alt_text, :is_primary, :display_order)
                """), {
                    'product_id': product_id,
                    'image_url': image_url,
                    'alt_text': alt_text,
                    'is_primary': is_primary,
                    'display_order': display_order
                })
            except Exception as e:
                print(f"  - Error adding image for product {product_id}: {str(e)}")
        
        # Add sample variants
        sample_variants = [
            (1, "Size", "S", 0.0, 50, "S"),
            (1, "Size", "M", 0.0, 75, "M"),
            (1, "Size", "L", 0.0, 60, "L"),
            (1, "Size", "XL", 50.0, 40, "XL"),
            (1, "Color", "White", 0.0, 30, "WHT"),
            (1, "Color", "Black", 0.0, 25, "BLK"),
            (1, "Color", "Navy", 0.0, 20, "NVY"),
            (2, "Size", "500ml", 0.0, 100, "500"),
            (2, "Size", "750ml", 100.0, 80, "750"),
            (2, "Size", "1L", 200.0, 60, "1L"),
            (3, "Color", "Brown", 0.0, 30, "BRN"),
            (3, "Color", "Black", 0.0, 25, "BLK"),
            (3, "Size", "Medium", 0.0, 40, "M"),
            (3, "Size", "Large", 150.0, 35, "L"),
        ]
        
        for product_id, variant_type, variant_value, price_adj, stock, sku_suffix in sample_variants:
            try:
                db.session.execute(text("""
                    INSERT IGNORE INTO product_variant 
                    (product_id, variant_type, variant_value, price_adjustment, stock_quantity, sku_suffix)
                    VALUES (:product_id, :variant_type, :variant_value, :price_adjustment, :stock_quantity, :sku_suffix)
                """), {
                    'product_id': product_id,
                    'variant_type': variant_type,
                    'variant_value': variant_value,
                    'price_adjustment': price_adj,
                    'stock_quantity': stock,
                    'sku_suffix': sku_suffix
                })
            except Exception as e:
                print(f"  - Error adding variant for product {product_id}: {str(e)}")
        
        db.session.commit()
        print("  ✓ Sample data added successfully")
        
    except Exception as e:
        print(f"  ✗ Error adding sample data: {str(e)}")

if __name__ == "__main__":
    run_migration()
