#!/usr/bin/env python3
"""
WebSocket Test Client
====================

Tests Flask-SocketIO functionality.
"""

import socketio
import time

def test_socketio_connection():
    """Test SocketIO connection"""
    print("🔌 Testing SocketIO Connection...")
    
    # Create SocketIO client
    sio = socketio.Client()
    
    @sio.event
    def connect():
        print("✅ Connected to SocketIO server")
        
    @sio.event
    def disconnect():
        print("✅ Disconnected from SocketIO server")
        
    @sio.event
    def connection_established(data):
        print(f"✅ Connection established: {data}")
        
    @sio.event
    def inventory_update(data):
        print(f"✅ Inventory update received: {data}")
        
    @sio.event
    def notification(data):
        print(f"✅ Notification received: {data}")
    
    try:
        # Connect to server
        sio.connect('http://localhost:5000')
        
        # Wait a bit
        time.sleep(2)
        
        # Disconnect
        sio.disconnect()
        
        print("🎉 SocketIO test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ SocketIO test failed: {e}")
        return False

if __name__ == '__main__':
    test_socketio_connection()
