#!/usr/bin/env python3
"""
Database Connection Test Script
===============================

This script tests the database configuration and connection for the Allora backend.
It verifies that all database settings are properly configured in app.py.

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_database_configuration():
    """Test database configuration and connection"""
    print("🔍 Testing Database Configuration...")
    print("=" * 50)
    
    # Check environment variables
    DATABASE_URL = os.getenv('DATABASE_URL')
    if not DATABASE_URL:
        print("❌ DATABASE_URL environment variable is not set!")
        return False
    
    print(f"✅ DATABASE_URL is configured")
    # Hide password in output for security
    safe_url = DATABASE_URL.split('@')[1] if '@' in DATABASE_URL else DATABASE_URL
    print(f"📍 Database location: {safe_url}")
    
    # Check database pool configuration
    print(f"🔧 Pool size: {os.getenv('DB_POOL_SIZE', '10')}")
    print(f"🔧 Max overflow: {os.getenv('DB_MAX_OVERFLOW', '20')}")
    print(f"🔧 Pool timeout: {os.getenv('DB_POOL_TIMEOUT', '30')}s")
    print(f"🔧 Pool recycle: {os.getenv('DB_POOL_RECYCLE', '3600')}s")
    
    print("\n🔍 Testing Database Connection...")
    print("-" * 30)
    
    try:
        # Import Flask app and database
        from app import app, db, check_database_connection
        
        with app.app_context():
            # Test basic connection
            if check_database_connection():
                print("✅ Database connection successful!")
                
                # Test database operations
                from sqlalchemy import text
                
                # Get database version
                try:
                    version_result = db.session.execute(text('SELECT VERSION()')).fetchone()
                    print(f"📊 Database version: {version_result[0]}")
                except Exception as e:
                    print(f"⚠️  Could not get database version: {e}")
                
                # Get database name
                try:
                    db_name_result = db.session.execute(text('SELECT DATABASE()')).fetchone()
                    print(f"🗄️  Database name: {db_name_result[0]}")
                except Exception as e:
                    print(f"⚠️  Could not get database name: {e}")
                
                # Count tables
                try:
                    table_count_result = db.session.execute(text(
                        "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()"
                    )).fetchone()
                    print(f"📋 Table count: {table_count_result[0]}")
                except Exception as e:
                    print(f"⚠️  Could not count tables: {e}")
                
                # Test connection pool
                try:
                    engine = db.engine
                    pool = engine.pool
                    print(f"🏊 Connection pool status:")
                    print(f"   - Pool size: {pool.size()}")
                    print(f"   - Checked in: {pool.checkedin()}")
                    print(f"   - Checked out: {pool.checkedout()}")
                    print(f"   - Overflow: {pool.overflow()}")
                    # Add invalid count if available (not all pool types have this)
                    if hasattr(pool, 'invalid'):
                        print(f"   - Invalid: {pool.invalid()}")
                except Exception as e:
                    print(f"⚠️  Could not get pool status: {e}")
                
                return True
            else:
                print("❌ Database connection failed!")
                return False
                
    except ImportError as e:
        print(f"❌ Could not import app modules: {e}")
        return False
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_redis_configuration():
    """Test Redis configuration"""
    print("\n🔍 Testing Redis Configuration...")
    print("-" * 30)
    
    try:
        from app import redis_client
        
        if redis_client is not None:
            redis_client.ping()
            print("✅ Redis connection successful!")
            
            # Get Redis info
            info = redis_client.info()
            print(f"📊 Redis version: {info.get('redis_version', 'unknown')}")
            print(f"👥 Connected clients: {info.get('connected_clients', 0)}")
            print(f"💾 Memory usage: {info.get('used_memory_human', 'unknown')}")
            
            return True
        else:
            print("⚠️  Redis client not initialized")
            return False
            
    except Exception as e:
        print(f"❌ Redis test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Allora Database Configuration Test")
    print("=" * 50)
    
    # Test database
    db_success = test_database_configuration()
    
    # Test Redis
    redis_success = test_redis_configuration()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 20)
    print(f"Database: {'✅ PASS' if db_success else '❌ FAIL'}")
    print(f"Redis: {'✅ PASS' if redis_success else '⚠️  WARNING'}")
    
    if db_success:
        print("\n🎉 Database configuration is working correctly!")
        print("✅ Your app.py file is properly configured for database operations.")
    else:
        print("\n❌ Database configuration has issues that need to be fixed.")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
