#!/usr/bin/env python3
"""
Direct Database Inspection using Raw SQL
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
import logging
from sqlalchemy import text

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def inspect_database_directly():
    """Use raw SQL to inspect database structure"""
    
    print("🔍 DIRECT DATABASE INSPECTION")
    print("="*80)
    
    with app.app_context():
        # Get all tables
        result = db.session.execute(text("SHOW TABLES"))
        tables = [row[0] for row in result]
        
        print(f"📊 Total tables found: {len(tables)}")
        
        # Check primary keys for each table
        tables_with_pk = []
        tables_without_pk = []
        
        for table_name in tables:
            try:
                # Check for primary key using SHOW KEYS
                pk_query = f"SHOW KEYS FROM `{table_name}` WHERE Key_name = 'PRIMARY'"
                pk_result = db.session.execute(text(pk_query))
                pk_rows = list(pk_result)
                
                if pk_rows:
                    tables_with_pk.append(table_name)
                    # Get the primary key column name
                    pk_column = pk_rows[0][4]  # Column_name is at index 4
                    print(f"✅ {table_name}: PRIMARY KEY ({pk_column})")
                else:
                    tables_without_pk.append(table_name)
                    print(f"❌ {table_name}: NO PRIMARY KEY")
                    
            except Exception as e:
                print(f"⚠️  Error checking {table_name}: {e}")
        
        print(f"\n📊 SUMMARY:")
        print(f"✅ Tables with primary keys: {len(tables_with_pk)}")
        print(f"❌ Tables without primary keys: {len(tables_without_pk)}")
        
        if tables_without_pk:
            print(f"\n⚠️  Tables missing primary keys:")
            for table in tables_without_pk[:20]:  # Show first 20
                print(f"  - {table}")
            if len(tables_without_pk) > 20:
                print(f"  ... and {len(tables_without_pk) - 20} more")
        
        return tables_with_pk, tables_without_pk

def check_table_structure(table_name):
    """Check detailed structure of a specific table"""
    
    with app.app_context():
        try:
            # Get column information
            desc_query = f"DESCRIBE `{table_name}`"
            result = db.session.execute(text(desc_query))
            
            print(f"\n📋 Structure of table '{table_name}':")
            print("Column Name | Type | Null | Key | Default | Extra")
            print("-" * 60)
            
            for row in result:
                field, type_, null, key, default, extra = row
                print(f"{field:<12} | {type_:<8} | {null:<4} | {key:<3} | {default or 'NULL':<7} | {extra}")
                
        except Exception as e:
            print(f"❌ Error checking table structure for {table_name}: {e}")

def fix_missing_primary_keys(tables_without_pk):
    """Fix tables that are missing primary keys"""
    
    print(f"\n🔧 FIXING MISSING PRIMARY KEYS")
    print("="*80)
    
    with app.app_context():
        fixed_count = 0
        error_count = 0
        
        for table_name in tables_without_pk[:10]:  # Fix first 10 tables
            try:
                # Check if table has an 'id' column
                desc_query = f"DESCRIBE `{table_name}`"
                result = db.session.execute(text(desc_query))
                columns = list(result)
                
                # Look for id column
                id_column_exists = any(col[0] == 'id' for col in columns)
                
                if id_column_exists:
                    # Add primary key to existing id column
                    try:
                        # First ensure the column is AUTO_INCREMENT
                        alter_query = f"""
                            ALTER TABLE `{table_name}` 
                            MODIFY COLUMN `id` INT NOT NULL AUTO_INCREMENT,
                            ADD PRIMARY KEY (`id`)
                        """
                        db.session.execute(text(alter_query))
                        db.session.commit()
                        
                        print(f"✅ Fixed primary key for '{table_name}'")
                        fixed_count += 1
                        
                    except Exception as e:
                        if "Multiple primary key defined" in str(e):
                            print(f"⏭️  '{table_name}' already has primary key")
                        else:
                            print(f"❌ Error fixing '{table_name}': {e}")
                            error_count += 1
                        db.session.rollback()
                else:
                    # Create id column and set as primary key
                    try:
                        alter_query = f"""
                            ALTER TABLE `{table_name}` 
                            ADD COLUMN `id` INT NOT NULL AUTO_INCREMENT PRIMARY KEY FIRST
                        """
                        db.session.execute(text(alter_query))
                        db.session.commit()
                        
                        print(f"✅ Added primary key column to '{table_name}'")
                        fixed_count += 1
                        
                    except Exception as e:
                        print(f"❌ Error adding primary key to '{table_name}': {e}")
                        error_count += 1
                        db.session.rollback()
                        
            except Exception as e:
                print(f"❌ Error processing '{table_name}': {e}")
                error_count += 1
        
        print(f"\n📊 REPAIR SUMMARY:")
        print(f"✅ Tables fixed: {fixed_count}")
        print(f"❌ Errors: {error_count}")

def main():
    print("🚀 Starting Direct Database Inspection...")
    
    # Inspect database structure
    tables_with_pk, tables_without_pk = inspect_database_directly()
    
    # Show detailed structure for a few problematic tables
    if tables_without_pk:
        print(f"\n🔍 DETAILED INSPECTION OF PROBLEMATIC TABLES")
        print("="*80)
        for table in tables_without_pk[:3]:  # Check first 3 tables
            check_table_structure(table)
    
    # Offer to fix missing primary keys
    if tables_without_pk:
        print(f"\n⚠️  Found {len(tables_without_pk)} tables without primary keys!")
        response = input("Do you want to fix the missing primary keys? (yes/no): ")
        if response.lower() == 'yes':
            fix_missing_primary_keys(tables_without_pk)
            
            # Re-inspect after fixes
            print(f"\n🔍 RE-INSPECTING AFTER FIXES")
            print("="*80)
            inspect_database_directly()
    
    print("\n✅ Database inspection completed!")

if __name__ == '__main__':
    main()
