#!/usr/bin/env python3
"""
Tracking System Test Suite
==========================

Comprehensive testing for the real-time tracking system functionality.
Tests all components and validates integration with the project.

Author: Allora Development Team
Date: 2025-07-13
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime, timedelta

def test_tracking_system_structure():
    """Test the tracking system module structure"""
    print("🔍 Testing Tracking System Structure")
    print("=" * 45)
    
    try:
        # Import tracking system module
        import tracking_system
        
        print("✅ Tracking system module imported successfully")
        
        # Test core classes
        required_classes = [
            'TrackingStatus', 'TrackingEventType', 'TrackingEvent', 
            'TrackingInfo', 'RealTimeTrackingSystem', 'CarrierStatusMapper'
        ]
        
        for class_name in required_classes:
            if hasattr(tracking_system, class_name):
                print(f"   ✅ {class_name}: Found")
            else:
                print(f"   ❌ {class_name}: Missing")
        
        # Test functions
        required_functions = ['get_tracking_system']
        for func_name in required_functions:
            if hasattr(tracking_system, func_name):
                print(f"   ✅ {func_name}: Found")
            else:
                print(f"   ❌ {func_name}: Missing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Tracking system import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Tracking system structure test error: {e}")
        return False

def test_tracking_dashboard_integration():
    """Test tracking dashboard integration"""
    print("\n📊 Testing Tracking Dashboard Integration")
    print("=" * 50)
    
    try:
        # Import tracking dashboard
        import tracking_dashboard
        
        print("✅ Tracking dashboard module imported successfully")
        
        # Test dashboard classes
        dashboard_classes = [
            'DashboardMetrics', 'DashboardFilter', 'TrackingDashboardService'
        ]
        
        for class_name in dashboard_classes:
            if hasattr(tracking_dashboard, class_name):
                print(f"   ✅ {class_name}: Found")
            else:
                print(f"   ❌ {class_name}: Missing")
        
        # Test blueprint
        if hasattr(tracking_dashboard, 'dashboard_bp'):
            print("   ✅ Dashboard blueprint: Found")
        else:
            print("   ❌ Dashboard blueprint: Missing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Tracking dashboard import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Tracking dashboard test error: {e}")
        return False

def test_webhook_handlers_integration():
    """Test webhook handlers integration"""
    print("\n🔗 Testing Webhook Handlers Integration")
    print("=" * 45)
    
    try:
        # Import webhook handlers
        import webhook_handlers
        
        print("✅ Webhook handlers module imported successfully")
        
        # Test webhook classes
        webhook_classes = [
            'WebhookCarrier', 'WebhookConfig', 'WebhookSecurity', 'WebhookEventProcessor'
        ]
        
        for class_name in webhook_classes:
            if hasattr(webhook_handlers, class_name):
                print(f"   ✅ {class_name}: Found")
            else:
                print(f"   ❌ {class_name}: Missing")
        
        # Test blueprint
        if hasattr(webhook_handlers, 'webhook_bp'):
            print("   ✅ Webhook blueprint: Found")
        else:
            print("   ❌ Webhook blueprint: Missing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Webhook handlers import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Webhook handlers test error: {e}")
        return False

def test_app_integration():
    """Test tracking system integration with main Flask app"""
    print("\n🔗 Testing Flask App Integration")
    print("=" * 40)
    
    try:
        # Import Flask app
        from app import app, db
        
        print("✅ Flask app imported successfully")
        
        # Test if tracking system is initialized
        with app.app_context():
            # Check if TrackingEvent model exists
            from app import TrackingEvent
            print("   ✅ TrackingEvent model: Found")
            
            # Check model fields
            tracking_fields = [
                'id', 'order_id', 'tracking_number', 'status', 
                'event_type', 'description', 'location', 'timestamp',
                'carrier_code', 'carrier_status'
            ]
            
            for field in tracking_fields:
                if hasattr(TrackingEvent, field):
                    print(f"   ✅ TrackingEvent.{field}: Found")
                else:
                    print(f"   ❌ TrackingEvent.{field}: Missing")
        
        # Test blueprint registration
        blueprints = [bp.name for bp in app.blueprints.values()]

        tracking_blueprints = ['tracking_dashboard', 'webhooks']
        for bp_name in tracking_blueprints:
            if bp_name in blueprints:
                print(f"   ✅ {bp_name} blueprint: Registered")
            else:
                print(f"   ❌ {bp_name} blueprint: Not registered")

        # Test registered routes
        tracking_routes = []
        for rule in app.url_map.iter_rules():
            if any(keyword in rule.rule for keyword in ['/admin/tracking', '/api/webhooks']):
                tracking_routes.append(f"{rule.rule} [{', '.join(rule.methods)}]")

        if tracking_routes:
            print("   ✅ Tracking routes registered:")
            for route in tracking_routes[:5]:  # Show first 5 routes
                print(f"      • {route}")
            if len(tracking_routes) > 5:
                print(f"      • ... and {len(tracking_routes) - 5} more routes")
        else:
            print("   ❌ No tracking routes found")
        
        return True
        
    except Exception as e:
        print(f"❌ App integration test error: {e}")
        return False

def test_tracking_system_functionality():
    """Test tracking system core functionality"""
    print("\n⚙️ Testing Tracking System Functionality")
    print("=" * 45)
    
    try:
        from app import app, db
        from tracking_system import get_tracking_system, TrackingStatus
        
        with app.app_context():
            # Get tracking system instance
            tracking_system = get_tracking_system(db.session)
            print("✅ Tracking system instance created")
            
            # Test tracking system methods
            methods_to_test = [
                'start_tracking', 'stop_tracking', 'get_tracking_info',
                'get_tracking_summary', 'get_active_trackings'
            ]
            
            for method_name in methods_to_test:
                if hasattr(tracking_system, method_name):
                    print(f"   ✅ {method_name}: Available")
                else:
                    print(f"   ❌ {method_name}: Missing")
            
            # Test tracking status enum
            status_values = [
                'LABEL_CREATED', 'PICKED_UP', 'IN_TRANSIT', 
                'OUT_FOR_DELIVERY', 'DELIVERED', 'EXCEPTION'
            ]
            
            for status in status_values:
                if hasattr(TrackingStatus, status):
                    print(f"   ✅ TrackingStatus.{status}: Available")
                else:
                    print(f"   ❌ TrackingStatus.{status}: Missing")
        
        return True

    except Exception as e:
        print(f"❌ Tracking system functionality test error: {e}")
        return False

def test_database_integration():
    """Test tracking system database integration"""
    print("\n🗄️ Testing Database Integration")
    print("=" * 35)

    try:
        from app import app, db, TrackingEvent, Order, Shipment

        with app.app_context():
            # Test database tables exist
            try:
                # Check if tables exist by querying them
                tracking_count = db.session.query(TrackingEvent).count()
                print(f"✅ TrackingEvent table: {tracking_count} records")

                order_count = db.session.query(Order).count()
                print(f"✅ Order table: {order_count} records")

                shipment_count = db.session.query(Shipment).count()
                print(f"✅ Shipment table: {shipment_count} records")

            except Exception as e:
                print(f"⚠️  Database query error: {e}")
                print("   This may indicate missing tables or connection issues")

            # Test model relationships
            try:
                # Test TrackingEvent relationships
                if hasattr(TrackingEvent, 'order'):
                    print("✅ TrackingEvent.order relationship: Found")
                else:
                    print("❌ TrackingEvent.order relationship: Missing")

                if hasattr(TrackingEvent, 'shipment'):
                    print("✅ TrackingEvent.shipment relationship: Found")
                else:
                    print("❌ TrackingEvent.shipment relationship: Missing")

                # Test Order backref
                if hasattr(Order, 'tracking_events'):
                    print("✅ Order.tracking_events backref: Found")
                else:
                    print("❌ Order.tracking_events backref: Missing")

            except Exception as e:
                print(f"❌ Relationship test error: {e}")

        return True

    except Exception as e:
        print(f"❌ Database integration test error: {e}")
        return False

def test_api_endpoints():
    """Test tracking system API endpoints"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 30)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test endpoints with correct URLs
    endpoints = [
        {
            'name': 'Dashboard Metrics',
            'url': f"{base_url}/admin/tracking/api/metrics",
            'description': 'Get tracking dashboard metrics'
        },
        {
            'name': 'Shipments List',
            'url': f"{base_url}/admin/tracking/api/shipments",
            'description': 'Get shipments list for dashboard'
        },
        {
            'name': 'Webhook Test',
            'url': f"{base_url}/api/webhooks/test",
            'description': 'Test webhook endpoint',
            'method': 'POST'
        },
        {
            'name': 'Blue Dart Webhook',
            'url': f"{base_url}/api/webhooks/blue-dart",
            'description': 'Blue Dart webhook endpoint',
            'method': 'POST'
        }
    ]
    
    for endpoint in endpoints:
        print(f"🔍 Testing: {endpoint['name']}")
        print(f"   URL: {endpoint['url']}")
        print(f"   Purpose: {endpoint['description']}")
        
        try:
            method = endpoint.get('method', 'GET')
            
            if method == 'GET':
                response = requests.get(endpoint['url'], timeout=10)
            else:
                # For POST endpoints, send test data
                test_data = {
                    'carrier': 'blue_dart',
                    'tracking_number': 'TEST123456',
                    'status': 'in_transit'
                }
                response = requests.post(endpoint['url'], json=test_data, timeout=10)
            
            print(f"   ✅ Status Code: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ Response Format: Valid JSON")
                    if isinstance(data, dict):
                        print(f"   ✅ Response Keys: {list(data.keys())[:5]}...")
                except:
                    print(f"   ⚠️  Response: Non-JSON content")
            else:
                print(f"   ⚠️  HTTP Status: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection Error: Server not running at {base_url}")
        except requests.exceptions.Timeout:
            print(f"   ❌ Timeout Error: Request took too long")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()

def analyze_tracking_system_purpose():
    """Analyze and explain the tracking system's purpose"""
    print("\n📋 Tracking System Purpose Analysis")
    print("=" * 45)
    
    print("🎯 PRIMARY PURPOSE:")
    print("   The Tracking System provides comprehensive real-time shipment")
    print("   tracking and monitoring for the Allora e-commerce platform.")
    print()
    
    print("🔧 KEY COMPONENTS:")
    print("   1. Real-Time Tracking System (tracking_system.py)")
    print("      • Monitors shipments across multiple carriers")
    print("      • Provides automated status synchronization")
    print("      • Handles exception detection and processing")
    print("      • Manages background polling and updates")
    print()
    
    print("   2. Tracking Dashboard (tracking_dashboard.py)")
    print("      • Provides comprehensive dashboard metrics")
    print("      • Shows carrier performance analytics")
    print("      • Displays shipment status distribution")
    print("      • Tracks delivery performance and exceptions")
    print()
    
    print("   3. Webhook Handlers (webhook_handlers.py)")
    print("      • Processes real-time carrier notifications")
    print("      • Handles secure webhook verification")
    print("      • Supports multiple carrier integrations")
    print("      • Provides event processing and deduplication")
    print()
    
    print("🏗️ INTEGRATION ARCHITECTURE:")
    print("   • Database Models: TrackingEvent, Shipment tables")
    print("   • Flask Blueprints: dashboard_bp, webhook_bp")
    print("   • Background Services: Continuous tracking threads")
    print("   • API Endpoints: Dashboard metrics and shipment data")
    print("   • Carrier Integration: Multi-carrier support system")
    print()
    
    print("📊 BUSINESS VALUE:")
    print("   • Real-time shipment visibility for customers")
    print("   • Proactive exception handling and notifications")
    print("   • Performance analytics for carrier optimization")
    print("   • Automated status updates reducing manual work")
    print("   • Enhanced customer experience through transparency")
    print()

def run_all_tests():
    """Run all tracking system tests"""
    print("🚀 Tracking System Test Suite")
    print("=" * 40)
    print()
    
    tests = [
        test_tracking_system_structure,
        test_tracking_dashboard_integration,
        test_webhook_handlers_integration,
        test_app_integration,
        test_tracking_system_functionality,
        test_database_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Analyze purpose
    analyze_tracking_system_purpose()
    
    # Test API endpoints (requires running server)
    print("⚠️  Note: API endpoint testing requires the server to be running")
    print("   Start the server with: python run_with_waitress.py")
    print()
    
    try:
        test_api_endpoints()
    except Exception as e:
        print(f"❌ API endpoint testing failed: {e}")
        print("   This is expected if the server is not running")
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Tracking system is properly integrated.")
    else:
        print("⚠️  Some tests failed. Please review the tracking system integration.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
