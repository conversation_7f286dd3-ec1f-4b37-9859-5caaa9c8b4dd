#!/usr/bin/env python3
"""
Complete Product Cleanup Script
This script will remove ALL products from:
1. PostgreSQL Database
2. Elasticsearch Index
3. Remove seed files
4. Clear frontend fallback data
"""

import os
import sys
import requests
import shutil
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def clear_elasticsearch_products():
    """Clear all products from Elasticsearch index"""
    print("🔍 Clearing Elasticsearch products...")
    
    try:
        # Delete the entire allora_products index
        response = requests.delete('http://localhost:9200/allora_products')
        
        if response.status_code == 200:
            print("✅ Elasticsearch index 'allora_products' deleted successfully")
            
            # Recreate the index (optional - it will be recreated automatically when needed)
            print("🔄 Recreating empty Elasticsearch index...")
            create_response = requests.put('http://localhost:9200/allora_products')
            if create_response.status_code == 200:
                print("✅ Empty Elasticsearch index recreated")
            else:
                print("ℹ️  Index will be recreated automatically when needed")
                
        elif response.status_code == 404:
            print("ℹ️  Elasticsearch index 'allora_products' doesn't exist")
        else:
            print(f"⚠️  Elasticsearch response: {response.status_code} - {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to Elasticsearch (localhost:9200)")
        print("   Make sure Elasticsearch is running")
    except Exception as e:
        print(f"❌ Error clearing Elasticsearch: {e}")

def clear_database_products():
    """Clear all products from PostgreSQL database"""
    print("🗄️  Clearing database products...")
    
    try:
        from app import app, db, Product, OrderItem, CartItem, Review, Wishlist
        
        with app.app_context():
            initial_count = Product.query.count()
            print(f"📊 Found {initial_count} products in database")
            
            if initial_count == 0:
                print("✅ Database is already empty!")
                return
            
            # Get all product IDs
            products = Product.query.all()
            product_ids = [p.id for p in products]
            
            # Delete related data first (to avoid foreign key constraints)
            cart_items_deleted = CartItem.query.filter(CartItem.product_id.in_(product_ids)).delete(synchronize_session=False)
            order_items_deleted = OrderItem.query.filter(OrderItem.product_id.in_(product_ids)).delete(synchronize_session=False)
            reviews_deleted = Review.query.filter(Review.product_id.in_(product_ids)).delete(synchronize_session=False)
            wishlist_deleted = Wishlist.query.filter(Wishlist.product_id.in_(product_ids)).delete(synchronize_session=False)
            
            # Delete products
            products_deleted = Product.query.delete()
            
            # Commit all changes
            db.session.commit()
            
            print(f"✅ Database cleanup completed:")
            print(f"   - Products deleted: {products_deleted}")
            print(f"   - Cart items deleted: {cart_items_deleted}")
            print(f"   - Order items deleted: {order_items_deleted}")
            print(f"   - Reviews deleted: {reviews_deleted}")
            print(f"   - Wishlist items deleted: {wishlist_deleted}")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Error clearing database: {e}")

def remove_seed_files():
    """Remove all seed and sample data files"""
    print("📁 Removing seed files...")
    
    seed_files = [
        "seed.py",
        "add_sample_products.py", 
        "seed_products.py",
        "Other Files/Setup Files (One-time use)/seed_products.py",
        "Other Files/Setup Files (One-time use)/add_sample_products.py",
        "Other Files/Setup Files (One-time use)",
        "Other Files/Test Files/update_product_data.py"
    ]
    
    removed_count = 0
    
    for file_path in seed_files:
        full_path = Path(file_path)
        if full_path.exists():
            try:
                if full_path.is_file():
                    full_path.unlink()
                    print(f"✅ Removed file: {file_path}")
                    removed_count += 1
                elif full_path.is_dir():
                    shutil.rmtree(full_path)
                    print(f"✅ Removed directory: {file_path}")
                    removed_count += 1
            except Exception as e:
                print(f"❌ Failed to remove {file_path}: {e}")
    
    print(f"📊 Removed {removed_count} seed files/directories")

def clear_frontend_fallback_data():
    """Remove hardcoded fallback data from frontend"""
    print("🎨 Clearing frontend fallback data...")
    
    frontend_file = Path("../frontend/src/components/SafeHome.js")
    
    if frontend_file.exists():
        try:
            content = frontend_file.read_text()
            
            # Replace the fallback products with empty array
            updated_content = content.replace(
                """        // Set fallback products for demo
        setProducts([
          {
            id: 1,
            name: 'Eco-Friendly Cotton T-Shirt',
            description: 'Sustainable organic cotton t-shirt',
            price: 899,
            image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400',
            sustainability_score: 9
          },
          {
            id: 2,
            name: 'Bamboo Water Bottle',
            description: 'Eco-friendly bamboo water bottle',
            price: 1299,
            image: 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=400',
            sustainability_score: 10
          }
        ]);""",
                """        // No fallback products - start with empty state
        setProducts([]);"""
            )
            
            if updated_content != content:
                frontend_file.write_text(updated_content)
                print("✅ Cleared frontend fallback data in SafeHome.js")
            else:
                print("ℹ️  No fallback data found in SafeHome.js")
                
        except Exception as e:
            print(f"❌ Error updating frontend file: {e}")
    else:
        print("ℹ️  Frontend SafeHome.js file not found")

def verify_cleanup():
    """Verify that all products have been cleared"""
    print("🔍 Verifying cleanup...")
    
    # Check database
    try:
        from app import app, db, Product
        with app.app_context():
            db_count = Product.query.count()
            print(f"📊 Database products: {db_count}")
    except:
        print("❌ Could not check database")
    
    # Check Elasticsearch
    try:
        response = requests.get('http://localhost:9200/allora_products/_count')
        if response.status_code == 200:
            es_count = response.json().get('count', 'unknown')
            print(f"📊 Elasticsearch products: {es_count}")
        else:
            print("📊 Elasticsearch products: index not found (empty)")
    except:
        print("❌ Could not check Elasticsearch")

def main():
    """Main cleanup function"""
    print("🧹 COMPLETE PRODUCT CLEANUP")
    print("=" * 50)
    print("This will remove ALL products from:")
    print("  • PostgreSQL Database")
    print("  • Elasticsearch Index")
    print("  • Seed files")
    print("  • Frontend fallback data")
    print("=" * 50)
    
    confirm = input("\nType 'CLEAR EVERYTHING' to confirm: ")
    if confirm != 'CLEAR EVERYTHING':
        print("❌ Operation cancelled.")
        return
    
    print("\n🚀 Starting complete cleanup...")
    
    # Step 1: Clear Elasticsearch
    clear_elasticsearch_products()
    
    # Step 2: Clear Database
    clear_database_products()
    
    # Step 3: Remove seed files
    remove_seed_files()
    
    # Step 4: Clear frontend fallback data
    clear_frontend_fallback_data()
    
    # Step 5: Verify cleanup
    print("\n" + "=" * 50)
    verify_cleanup()
    
    print("\n✅ COMPLETE CLEANUP FINISHED!")
    print("🎉 Your Allora project now has a clean slate!")
    print("\nNext steps:")
    print("  • Restart your backend server")
    print("  • Refresh your frontend")
    print("  • Add new products through the admin panel or seller dashboard")

if __name__ == "__main__":
    main()
