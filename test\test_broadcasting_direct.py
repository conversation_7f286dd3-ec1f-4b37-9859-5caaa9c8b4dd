#!/usr/bin/env python3
"""
Direct Broadcasting Test
========================

Tests the actual broadcasting functionality by connecting a client
and triggering broadcasts directly through the SocketIO instance.

Author: Allora Development Team
Date: 2025-07-13
"""

import socketio as sio_client
import time
import threading
from datetime import datetime

def test_direct_broadcasting():
    """Test broadcasting directly through SocketIO instance"""
    print("🧪 Direct Broadcasting Test")
    print("=" * 30)
    
    # Create client
    client = sio_client.Client()
    events_received = []
    connected = False
    
    @client.event
    def connect():
        nonlocal connected
        connected = True
        print("✅ Client connected!")
        
    @client.event
    def disconnect():
        nonlocal connected
        connected = False
        print("❌ Client disconnected")
        
    @client.event
    def inventory_update(data):
        events_received.append(('inventory_update', data))
        print(f"📦 RECEIVED: Inventory update - {data}")
        
    @client.event
    def price_update(data):
        events_received.append(('price_update', data))
        print(f"💰 RECEIVED: Price update - {data}")
        
    @client.event
    def cart_update(data):
        events_received.append(('cart_update', data))
        print(f"🛒 RECEIVED: Cart update - {data}")
        
    @client.event
    def notification(data):
        events_received.append(('notification', data))
        print(f"🔔 RECEIVED: Notification - {data}")
        
    @client.event
    def admin_notification(data):
        events_received.append(('admin_notification', data))
        print(f"👑 RECEIVED: Admin notification - {data}")
    
    try:
        # Connect client
        print("🔌 Connecting client...")
        client.connect('http://localhost:5000', wait_timeout=10)
        
        if not connected:
            print("❌ Failed to connect client")
            return False
        
        # Wait for connection to stabilize
        time.sleep(2)
        
        # Test 1: Direct SocketIO instance access
        print("\n🧪 Test 1: Direct SocketIO Instance Access")
        try:
            from app import socketio as app_socketio
            if app_socketio:
                print("✅ SocketIO instance found in app")

                # Test direct emit
                print("📡 Testing direct emit...")
                app_socketio.emit('inventory_update', {
                    'type': 'inventory_updated',
                    'product_id': 999,
                    'new_quantity': 100,
                    'timestamp': datetime.now().isoformat()
                })

                time.sleep(2)

                if any(e[0] == 'inventory_update' for e in events_received):
                    print("✅ Direct emit test PASSED")
                else:
                    print("❌ Direct emit test FAILED")
            else:
                print("❌ SocketIO instance not found in app")
        except Exception as e:
            print(f"❌ Direct SocketIO test failed: {e}")
        
        # Test 2: Broadcasting functions
        print("\n🧪 Test 2: Broadcasting Functions")
        try:
            from flask_socketio_manager import broadcast_inventory_update
            
            print("📦 Testing broadcast_inventory_update...")
            broadcast_inventory_update(product_id=888, new_quantity=50, old_quantity=75)
            
            time.sleep(2)
            
            inventory_events = [e for e in events_received if e[0] == 'inventory_update' and e[1].get('product_id') == 888]
            if inventory_events:
                print("✅ Broadcast function test PASSED")
            else:
                print("❌ Broadcast function test FAILED")
                
        except Exception as e:
            print(f"❌ Broadcast function test failed: {e}")
        
        # Test 3: Manager instance
        print("\n🧪 Test 3: Manager Instance")
        try:
            from flask_socketio_manager import socketio_manager
            
            if socketio_manager and socketio_manager.socketio:
                print("✅ Manager instance has SocketIO")
                
                # Test manager broadcast
                print("📡 Testing manager broadcast...")
                socketio_manager.socketio.emit('price_update', {
                    'type': 'price_updated',
                    'product_id': 777,
                    'new_price': 29.99,
                    'timestamp': datetime.now().isoformat()
                })
                
                time.sleep(2)
                
                price_events = [e for e in events_received if e[0] == 'price_update' and e[1].get('product_id') == 777]
                if price_events:
                    print("✅ Manager broadcast test PASSED")
                else:
                    print("❌ Manager broadcast test FAILED")
            else:
                print("❌ Manager instance does not have SocketIO")
                
        except Exception as e:
            print(f"❌ Manager instance test failed: {e}")
        
        # Disconnect
        client.disconnect()
        
        # Results
        print(f"\n📊 Total events received: {len(events_received)}")
        for event_type, event_data in events_received:
            print(f"   - {event_type}: {event_data.get('product_id', 'N/A')}")
        
        if len(events_received) > 0:
            print("\n🎉 Broadcasting is working!")
            return True
        else:
            print("\n❌ No events received - broadcasting not working")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_socketio_availability():
    """Test if SocketIO is available and properly initialized"""
    print("\n🔍 SocketIO Availability Test")
    print("=" * 30)
    
    results = {}
    
    # Test 1: App module SocketIO
    try:
        from app import socketio as app_socketio
        if app_socketio:
            print("✅ app.socketio is available")
            print(f"   Type: {type(app_socketio)}")
            results['app_socketio'] = True
        else:
            print("❌ app.socketio is None")
            results['app_socketio'] = False
    except Exception as e:
        print(f"❌ app.socketio import failed: {e}")
        results['app_socketio'] = False
    
    # Test 2: Manager SocketIO
    try:
        from flask_socketio_manager import socketio_manager
        if socketio_manager and socketio_manager.socketio:
            print("✅ socketio_manager.socketio is available")
            print(f"   Type: {type(socketio_manager.socketio)}")
            results['manager_socketio'] = True
        else:
            print("❌ socketio_manager.socketio is None")
            results['manager_socketio'] = False
    except Exception as e:
        print(f"❌ socketio_manager import failed: {e}")
        results['manager_socketio'] = False
    
    # Test 3: Get instance function
    try:
        from flask_socketio_manager import get_socketio_instance
        instance = get_socketio_instance()
        if instance:
            print("✅ get_socketio_instance() returns instance")
            print(f"   Type: {type(instance)}")
            results['get_instance'] = True
        else:
            print("❌ get_socketio_instance() returns None")
            results['get_instance'] = False
    except Exception as e:
        print(f"❌ get_socketio_instance() failed: {e}")
        results['get_instance'] = False
    
    return results

def main():
    """Main test function"""
    print("🚀 Direct Broadcasting Test Suite")
    print("=" * 40)
    
    # Test SocketIO availability first
    availability = test_socketio_availability()
    
    # Run broadcasting test if SocketIO is available
    if any(availability.values()):
        broadcasting_works = test_direct_broadcasting()
    else:
        print("\n⚠️  Skipping broadcasting test - SocketIO not available")
        broadcasting_works = False
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 TEST SUMMARY")
    print("=" * 40)
    
    print("SocketIO Availability:")
    for test, result in availability.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test}: {status}")
    
    print(f"\nBroadcasting Test: {'✅ PASS' if broadcasting_works else '❌ FAIL'}")
    
    # Overall assessment
    if broadcasting_works:
        print("\n🎉 BROADCASTING IS WORKING!")
        print("✅ Your Flask-SocketIO system can broadcast events successfully!")
    elif any(availability.values()):
        print("\n⚠️  SocketIO available but broadcasting has issues")
        print("💡 Check event handlers and client connection")
    else:
        print("\n❌ SocketIO not properly initialized")
        print("💡 Check Flask-SocketIO setup in app.py")
    
    return broadcasting_works

if __name__ == '__main__':
    main()
