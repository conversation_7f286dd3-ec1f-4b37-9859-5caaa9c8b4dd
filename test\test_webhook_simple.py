#!/usr/bin/env python3
"""
Simple Webhook System Test
==========================

Quick test of webhook system functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_webhook_imports():
    """Test webhook module imports"""
    print("🔍 Testing Webhook Imports")
    print("=" * 30)
    
    try:
        import webhook_handlers
        print("✅ webhook_handlers imported")
        
        # Test classes
        classes = ['WebhookCarrier', 'WebhookConfig', 'WebhookSecurity', 'WebhookEventProcessor']
        for cls in classes:
            if hasattr(webhook_handlers, cls):
                print(f"   ✅ {cls}: Found")
            else:
                print(f"   ❌ {cls}: Missing")
        
        # Test configurations
        if hasattr(webhook_handlers, 'WEBHOOK_CONFIGS'):
            configs = webhook_handlers.WEBHOOK_CONFIGS
            print(f"   ✅ WEBHOOK_CONFIGS: {len(configs)} carriers")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_webhook_security():
    """Test webhook security"""
    print("\n🔒 Testing Webhook Security")
    print("=" * 30)
    
    try:
        from webhook_handlers import WebhookSecurity
        
        # Test signature generation
        payload = b'{"test": "data"}'
        secret = "test_secret"
        
        signature = WebhookSecurity.generate_signature(payload, secret, "hmac_sha256")
        print(f"✅ Signature generated: {signature[:20]}...")
        
        # Test verification
        is_valid = WebhookSecurity.verify_signature(payload, signature, secret, "hmac_sha256")
        print(f"✅ Signature verification: {is_valid}")
        
        return True
    except Exception as e:
        print(f"❌ Security test error: {e}")
        return False

def test_app_integration():
    """Test webhook integration with Flask app"""
    print("\n🔗 Testing App Integration")
    print("=" * 30)
    
    try:
        from app import app
        print("✅ Flask app imported")
        
        # Check blueprint registration
        blueprints = [bp.name for bp in app.blueprints.values()]
        if 'webhooks' in blueprints:
            print("✅ Webhooks blueprint registered")
        else:
            print("❌ Webhooks blueprint not registered")
        
        # Check routes
        webhook_routes = []
        for rule in app.url_map.iter_rules():
            if '/api/webhooks' in rule.rule:
                webhook_routes.append(rule.rule)
        
        print(f"✅ Webhook routes: {len(webhook_routes)} found")
        for route in webhook_routes[:3]:  # Show first 3
            print(f"   • {route}")
        
        return True
    except Exception as e:
        print(f"❌ App integration error: {e}")
        return False

def analyze_webhook_files():
    """Analyze webhook-related files in the project"""
    print("\n📁 Webhook Files Analysis")
    print("=" * 30)
    
    webhook_files = [
        {
            'file': 'webhook_handlers.py',
            'purpose': 'Main webhook processing system',
            'features': [
                'Multi-carrier webhook support',
                'Signature verification',
                'Event processing',
                'Flask blueprint with endpoints'
            ]
        },
        {
            'file': 'order_fulfillment/fulfillment_api.py',
            'purpose': 'Fulfillment webhook endpoints',
            'features': [
                'Carrier tracking webhooks',
                'Status update processing',
                'Database integration'
            ]
        },
        {
            'file': 'app.py (webhook sections)',
            'purpose': 'Inventory and payment webhooks',
            'features': [
                'Inventory update webhooks',
                'Sales channel webhooks',
                'Payment gateway webhooks'
            ]
        }
    ]
    
    for file_info in webhook_files:
        print(f"📄 {file_info['file']}")
        print(f"   Purpose: {file_info['purpose']}")
        print("   Features:")
        for feature in file_info['features']:
            print(f"     • {feature}")
        print()

def main():
    """Main test function"""
    print("🚀 Simple Webhook System Test")
    print("=" * 40)
    print()
    
    tests = [
        test_webhook_imports,
        test_webhook_security,
        test_app_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")
            results.append(False)
    
    analyze_webhook_files()
    
    # Summary
    passed = sum(results)
    total = len(results)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All webhook tests passed!")
    else:
        print("⚠️  Some webhook tests failed")

if __name__ == "__main__":
    main()
