#!/usr/bin/env python3
"""
Shiprocket Environment Setup Script
==================================

Interactive script to configure Shiprocket environment variables
for the Allora backend shipping integration.

Author: Allora Development Team
Date: 2025-07-14
"""

import os
import sys

def print_header(title):
    """Print section header"""
    print(f"\n{'='*60}")
    print(f"{title.center(60)}")
    print(f"{'='*60}")

def print_info(message):
    """Print info message"""
    print(f"ℹ️  {message}")

def print_success(message):
    """Print success message"""
    print(f"✅ {message}")

def print_warning(message):
    """Print warning message"""
    print(f"⚠️  {message}")

def setup_shiprocket_environment():
    """Interactive setup for Shiprocket environment variables"""
    
    print_header("SHIPROCKET ENVIRONMENT SETUP")
    print("This script will help you configure Shiprocket integration")
    print("for your Allora e-commerce backend.\n")
    
    # Check if user has Shiprocket account
    print_info("Before proceeding, ensure you have:")
    print("   • A Shiprocket account (free signup at https://www.shiprocket.in/)")
    print("   • At least one pickup location configured")
    print("   • Your login email and password ready")
    
    proceed = input("\nDo you have a Shiprocket account ready? (y/n): ").strip().lower()
    
    if proceed != 'y':
        print_info("Please create a Shiprocket account first:")
        print("   1. Visit: https://www.shiprocket.in/")
        print("   2. Click 'Sign Up' and complete registration")
        print("   3. Verify your email and phone number")
        print("   4. Add at least one pickup location")
        print("   5. Come back and run this script again")
        return False
    
    # Collect Shiprocket credentials
    print_header("SHIPROCKET CREDENTIALS")
    
    email = input("Shiprocket Email: ").strip()
    if not email:
        print_warning("Email is required!")
        return False
    
    password = input("Shiprocket Password: ").strip()
    if not password:
        print_warning("Password is required!")
        return False
    
    # Environment selection
    print_header("ENVIRONMENT SELECTION")
    print("1. Sandbox/Testing (recommended for development)")
    print("2. Production (for live orders)")
    
    env_choice = input("Choose environment (1 or 2): ").strip()
    sandbox = env_choice != '2'
    
    # Feature configuration
    print_header("FEATURE CONFIGURATION")
    
    cod_enabled = input("Enable Cash on Delivery (COD)? (y/n): ").strip().lower() == 'y'
    international_enabled = input("Enable International Shipping? (y/n): ").strip().lower() == 'y'
    insurance_enabled = input("Enable Insurance? (y/n): ").strip().lower() == 'y'
    auto_awb = input("Auto-generate AWB (shipping labels)? (y/n): ").strip().lower() == 'y'
    auto_pickup = input("Auto-schedule pickups? (y/n): ").strip().lower() == 'y'
    
    # Default package dimensions
    print_header("DEFAULT PACKAGE SETTINGS")
    print("These will be used when package dimensions are not specified:")
    
    length = input("Default Length (cm) [10]: ").strip() or "10"
    width = input("Default Width (cm) [10]: ").strip() or "10"
    height = input("Default Height (cm) [10]: ").strip() or "10"
    weight = input("Default Weight (kg) [0.5]: ").strip() or "0.5"
    
    # Pickup location
    pickup_location = input("Default Pickup Location Name [primary]: ").strip() or "primary"
    
    # Generate environment variables
    env_vars = {
        "SHIPROCKET_EMAIL": email,
        "SHIPROCKET_PASSWORD": password,
        "SHIPROCKET_SANDBOX": str(sandbox).lower(),
        "SHIPROCKET_BASE_URL": "https://apiv2.shiprocket.in/v1",
        "SHIPROCKET_STAGING_URL": "https://staging-apiv2.shiprocket.in/v1",
        "SHIPROCKET_DEFAULT_PICKUP_LOCATION": pickup_location,
        "SHIPROCKET_AUTO_AWB": str(auto_awb).lower(),
        "SHIPROCKET_AUTO_PICKUP": str(auto_pickup).lower(),
        "SHIPROCKET_ENABLE_COD": str(cod_enabled).lower(),
        "SHIPROCKET_ENABLE_INTERNATIONAL": str(international_enabled).lower(),
        "SHIPROCKET_ENABLE_INSURANCE": str(insurance_enabled).lower(),
        "SHIPROCKET_DEFAULT_LENGTH": length,
        "SHIPROCKET_DEFAULT_WIDTH": width,
        "SHIPROCKET_DEFAULT_HEIGHT": height,
        "SHIPROCKET_DEFAULT_WEIGHT": weight,
    }
    
    # Generate configuration text
    config_lines = [
        "\n# ===========================================",
        "# SHIPROCKET SHIPPING CONFIGURATION",
        "# ===========================================",
        "",
        "# Account Credentials",
        f"SHIPROCKET_EMAIL={email}",
        f"SHIPROCKET_PASSWORD={password}",
        "",
        "# Environment Settings",
        f"SHIPROCKET_SANDBOX={str(sandbox).lower()}",
        "SHIPROCKET_BASE_URL=https://apiv2.shiprocket.in/v1",
        "SHIPROCKET_STAGING_URL=https://staging-apiv2.shiprocket.in/v1",
        "",
        "# Default Configuration",
        f"SHIPROCKET_DEFAULT_PICKUP_LOCATION={pickup_location}",
        f"SHIPROCKET_AUTO_AWB={str(auto_awb).lower()}",
        f"SHIPROCKET_AUTO_PICKUP={str(auto_pickup).lower()}",
        "",
        "# Feature Flags",
        f"SHIPROCKET_ENABLE_COD={str(cod_enabled).lower()}",
        f"SHIPROCKET_ENABLE_INTERNATIONAL={str(international_enabled).lower()}",
        f"SHIPROCKET_ENABLE_INSURANCE={str(insurance_enabled).lower()}",
        "",
        "# Default Package Dimensions",
        f"SHIPROCKET_DEFAULT_LENGTH={length}",
        f"SHIPROCKET_DEFAULT_WIDTH={width}",
        f"SHIPROCKET_DEFAULT_HEIGHT={height}",
        f"SHIPROCKET_DEFAULT_WEIGHT={weight}",
        ""
    ]
    
    config_text = "\n".join(config_lines)
    
    # Show configuration
    print_header("GENERATED CONFIGURATION")
    print(config_text)
    
    # Ask to append to .env
    print_header("SAVE CONFIGURATION")
    choice = input("Add this configuration to your .env file? (y/n): ").strip().lower()
    
    if choice == 'y':
        try:
            with open('.env', 'a') as f:
                f.write(config_text)
            print_success("Configuration added to .env file!")
            
            # Test the configuration
            test_choice = input("\nTest the configuration now? (y/n): ").strip().lower()
            if test_choice == 'y':
                print_info("Testing Shiprocket configuration...")
                os.system("python test_shiprocket_integration.py")
            
            return True
            
        except Exception as e:
            print_warning(f"Error writing to .env file: {e}")
            print_info("Please manually add the configuration to your .env file.")
            return False
    else:
        print_info("Please manually add the above configuration to your .env file.")
        return True

def show_shiprocket_benefits():
    """Show Shiprocket benefits and features"""
    
    print_header("SHIPROCKET BENEFITS")
    
    benefits = [
        "✅ 17+ Courier Partners (Blue Dart, FedEx, Delhivery, etc.)",
        "✅ Single API for Multiple Carriers",
        "✅ Automatic Rate Comparison",
        "✅ Real-time Tracking",
        "✅ COD & Prepaid Support",
        "✅ International Shipping (220+ countries)",
        "✅ Pickup Scheduling",
        "✅ Return Management (RTO)",
        "✅ Weight Reconciliation",
        "✅ Dashboard & Analytics",
        "✅ Webhook Support",
        "✅ No Setup Fee",
        "✅ Pay per Shipment"
    ]
    
    for benefit in benefits:
        print(f"   {benefit}")
    
    print_header("PRICING OVERVIEW")
    print("   • Surface (3-5 days): ₹30-90 for 500g")
    print("   • Express (1-2 days): ₹50-150 for 500g")
    print("   • COD Charges: ₹15-25 per shipment")
    print("   • International: Starting ₹800 for 500g")
    print("   • No monthly fees - pay only per shipment")

def show_next_steps():
    """Show next steps after configuration"""
    
    print_header("NEXT STEPS")
    
    steps = [
        "1. ✅ Configure environment variables (this script)",
        "2. ⏳ Test API connection",
        "3. ⏳ Add pickup locations in Shiprocket dashboard",
        "4. ⏳ Test rate calculation",
        "5. ⏳ Create test shipment",
        "6. ⏳ Test tracking functionality",
        "7. ⏳ Configure webhooks (optional)",
        "8. ⏳ Go live with production credentials"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print_info("\nAfter configuration, your Allora backend will support:")
    print("   • POST /api/fulfillment/rates - Calculate shipping rates")
    print("   • POST /api/fulfillment/shipments - Create shipments")
    print("   • GET /api/fulfillment/shipments/{id}/track - Track shipments")
    print("   • POST /api/fulfillment/pickups - Schedule pickups")

def main():
    """Main setup function"""
    
    print("🚚 Shiprocket Integration Setup for Allora Backend")
    print("=" * 60)
    
    if not os.path.exists('.env'):
        print_warning(".env file not found!")
        print_info("Please make sure you're running this script from the backend directory.")
        return
    
    print("Choose an option:")
    print("1. Setup Shiprocket environment variables")
    print("2. Show Shiprocket benefits")
    print("3. Show next steps")
    print("4. Exit")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == '1':
        if setup_shiprocket_environment():
            print_success("\n🎉 Shiprocket environment setup complete!")
            print_info("Your Allora backend now supports Shiprocket shipping.")
            show_next_steps()
    elif choice == '2':
        show_shiprocket_benefits()
    elif choice == '3':
        show_next_steps()
    elif choice == '4':
        print_info("👋 Goodbye!")
    else:
        print_warning("Invalid choice. Please run the script again.")

if __name__ == "__main__":
    main()
