#!/usr/bin/env python3
"""
Complete Database Reset and Seeding Script
==========================================

This script:
1. Completely clears all database data
2. Seeds the database with sustainable products
3. Reindexes Elasticsearch

Usage:
    python reset_and_seed.py [--products=100] [--confirm]
"""

import os
import sys
import subprocess
import argparse

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

def clear_database():
    """Clear all database tables"""
    print("🗑️  CLEARING DATABASE")
    print("=" * 30)
    
    with app.app_context():
        try:
            engine = db.engine
            
            with engine.connect() as connection:
                # Disable foreign key checks
                print("🔓 Disabling foreign key checks...")
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                
                # Get all table names
                result = connection.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
                
                print(f"📊 Clearing {len(tables)} tables...")
                
                # Truncate each table
                for table in tables:
                    try:
                        connection.execute(text(f"TRUNCATE TABLE `{table}`"))
                        print(f"  ✅ {table}")
                    except Exception as e:
                        try:
                            connection.execute(text(f"DELETE FROM `{table}`"))
                            print(f"  ✅ {table} (deleted)")
                        except Exception as e2:
                            print(f"  ⚠️  {table} (failed)")
                
                # Re-enable foreign key checks
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                connection.commit()
            
            print("✅ Database cleared successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error clearing database: {e}")
            return False

def run_seeding(num_products=100):
    """Run the seeding script"""
    print(f"\n🌱 SEEDING DATABASE WITH {num_products} PRODUCTS")
    print("=" * 40)
    
    try:
        # Run the seeding script
        cmd = [sys.executable, "seed_database.py", f"--products={num_products}"]
        print(f"🚀 Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ Database seeding completed successfully!")
            print("\n📋 Seeding output:")
            print(result.stdout)
            return True
        else:
            print("❌ Database seeding failed!")
            print("\n📋 Error output:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error running seeding: {e}")
        return False

def run_reindexing():
    """Run Elasticsearch reindexing"""
    print(f"\n🔍 REINDEXING ELASTICSEARCH")
    print("=" * 30)
    
    try:
        # Run the reindexing script
        cmd = [sys.executable, "reindex_products.py"]
        print(f"🚀 Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ Elasticsearch reindexing completed successfully!")
            print("\n📋 Reindexing output:")
            print(result.stdout)
            return True
        else:
            print("❌ Elasticsearch reindexing failed!")
            print("\n📋 Error output:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error running reindexing: {e}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Reset database and seed with sustainable products')
    parser.add_argument('--products', type=int, default=100, 
                       help='Number of products to create (default: 100)')
    parser.add_argument('--confirm', action='store_true', 
                       help='Skip confirmation prompt')
    
    args = parser.parse_args()
    
    # Confirmation
    if not args.confirm:
        print("⚠️  WARNING: This will DELETE ALL DATA and reseed the database!")
        print("This action cannot be undone.")
        print(f"Will create {args.products} sustainable products.")
        print("")
        response = input("Are you sure you want to continue? Type 'YES' to confirm: ")
        
        if response != 'YES':
            print("❌ Operation cancelled.")
            return False
    
    print("🚀 STARTING COMPLETE DATABASE RESET")
    print("=" * 50)
    
    # Step 1: Clear database
    if not clear_database():
        print("❌ Failed to clear database. Aborting.")
        return False
    
    # Step 2: Seed database
    if not run_seeding(args.products):
        print("❌ Failed to seed database. Aborting.")
        return False
    
    # Step 3: Reindex Elasticsearch
    if not run_reindexing():
        print("⚠️  Database seeded but Elasticsearch reindexing failed.")
        print("You can manually run: python reindex_products.py")
        return False
    
    print("\n🎉 COMPLETE SUCCESS!")
    print("=" * 50)
    print("✅ Database cleared")
    print(f"✅ {args.products} sustainable products created")
    print("✅ Elasticsearch reindexed")
    print("\n🔍 Test autocomplete with queries like:")
    print("  - 'organic'")
    print("  - 'bamboo'")
    print("  - 'eco'")
    print("  - 'sustainable'")
    print("  - 'patagonia'")
    print("  - 'fair trade'")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
