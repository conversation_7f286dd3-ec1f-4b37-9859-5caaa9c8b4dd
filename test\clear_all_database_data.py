#!/usr/bin/env python3
"""
Complete Database Data Clearing Script
=====================================

This script completely clears all data from all database tables and shows
the results in a readable format with progress tracking and statistics.

Features:
- Lists all tables before clearing
- Shows progress during clearing
- Displays before/after statistics
- Handles foreign key constraints safely
- Provides detailed error reporting
- Shows final summary

Usage:
    python clear_all_database_data.py [--confirm]
"""

import os
import sys
import argparse
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

def get_table_row_counts(connection):
    """Get row counts for all tables"""
    try:
        # Get all table names
        result = connection.execute(text("SHOW TABLES"))
        tables = [row[0] for row in result.fetchall()]
        
        table_counts = {}
        for table in tables:
            try:
                count_result = connection.execute(text(f"SELECT COUNT(*) FROM `{table}`"))
                count = count_result.fetchone()[0]
                table_counts[table] = count
            except Exception as e:
                table_counts[table] = f"Error: {str(e)}"
        
        return table_counts
    except Exception as e:
        print(f"❌ Error getting table counts: {e}")
        return {}

def display_table_statistics(table_counts, title):
    """Display table statistics in a readable format"""
    print(f"\n📊 {title}")
    print("=" * 60)
    
    if not table_counts:
        print("❌ No table data available")
        return
    
    # Sort tables by name for consistent display
    sorted_tables = sorted(table_counts.items())
    
    total_rows = 0
    tables_with_data = 0
    
    for table, count in sorted_tables:
        if isinstance(count, int):
            if count > 0:
                print(f"📋 {table:<30} {count:>8} rows")
                total_rows += count
                tables_with_data += 1
            else:
                print(f"⚪ {table:<30} {count:>8} rows")
        else:
            print(f"❌ {table:<30} {str(count)}")
    
    print("-" * 60)
    print(f"📈 Total Tables: {len(sorted_tables)}")
    print(f"📊 Tables with Data: {tables_with_data}")
    print(f"🔢 Total Rows: {total_rows:,}")

def clear_all_database_data():
    """Clear all data from all database tables"""
    print("🚀 STARTING COMPLETE DATABASE DATA CLEARING")
    print("=" * 70)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    with app.app_context():
        try:
            engine = db.engine
            
            with engine.connect() as connection:
                print("\n🔍 ANALYZING DATABASE BEFORE CLEARING")
                print("-" * 50)
                
                # Get table counts before clearing
                print("📋 Getting table statistics...")
                before_counts = get_table_row_counts(connection)
                display_table_statistics(before_counts, "DATABASE STATE BEFORE CLEARING")
                
                # Calculate total rows to be deleted
                total_rows_before = sum(count for count in before_counts.values() if isinstance(count, int))
                
                if total_rows_before == 0:
                    print("\n✅ Database is already empty!")
                    return True
                
                print(f"\n⚠️  About to delete {total_rows_before:,} rows from {len(before_counts)} tables")
                
                print("\n🗑️  STARTING DATA CLEARING PROCESS")
                print("-" * 50)
                
                # Disable foreign key checks
                print("🔓 Disabling foreign key constraints...")
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                
                # Get all table names
                result = connection.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
                
                print(f"📊 Found {len(tables)} tables to clear")
                print("\n🔄 Clearing tables:")
                
                cleared_tables = 0
                failed_tables = []
                
                # Clear each table
                for i, table in enumerate(tables, 1):
                    try:
                        # Show progress
                        progress = (i / len(tables)) * 100
                        print(f"  [{progress:5.1f}%] 🗑️  Clearing: {table:<30}", end="")
                        
                        # Try TRUNCATE first (faster)
                        try:
                            connection.execute(text(f"TRUNCATE TABLE `{table}`"))
                            print(" ✅ TRUNCATED")
                        except Exception:
                            # If TRUNCATE fails, try DELETE
                            connection.execute(text(f"DELETE FROM `{table}`"))
                            print(" ✅ DELETED")
                        
                        cleared_tables += 1
                        
                    except Exception as e:
                        print(f" ❌ FAILED: {str(e)[:50]}...")
                        failed_tables.append((table, str(e)))
                
                # Re-enable foreign key checks
                print("\n🔒 Re-enabling foreign key constraints...")
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                
                # Commit the transaction
                connection.commit()
                
                print("\n🔍 ANALYZING DATABASE AFTER CLEARING")
                print("-" * 50)
                
                # Get table counts after clearing
                after_counts = get_table_row_counts(connection)
                display_table_statistics(after_counts, "DATABASE STATE AFTER CLEARING")
                
                # Calculate results
                total_rows_after = sum(count for count in after_counts.values() if isinstance(count, int))
                rows_deleted = total_rows_before - total_rows_after
                
                print("\n📈 CLEARING RESULTS SUMMARY")
                print("=" * 50)
                print(f"✅ Tables Successfully Cleared: {cleared_tables}/{len(tables)}")
                print(f"❌ Tables Failed to Clear: {len(failed_tables)}")
                print(f"🔢 Rows Before Clearing: {total_rows_before:,}")
                print(f"🔢 Rows After Clearing: {total_rows_after:,}")
                print(f"🗑️  Total Rows Deleted: {rows_deleted:,}")
                
                if failed_tables:
                    print(f"\n❌ FAILED TABLES ({len(failed_tables)}):")
                    print("-" * 30)
                    for table, error in failed_tables:
                        print(f"  • {table}: {error[:60]}...")
                
                print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                if total_rows_after == 0 and len(failed_tables) == 0:
                    print("\n🎉 SUCCESS: Database completely cleared!")
                    return True
                elif total_rows_after == 0:
                    print("\n⚠️  WARNING: Database cleared but some tables had errors")
                    return True
                else:
                    print(f"\n❌ ERROR: {total_rows_after:,} rows still remain in database")
                    return False
                    
        except Exception as e:
            print(f"\n❌ CRITICAL ERROR during database clearing: {e}")
            return False

def main():
    """Main function with confirmation"""
    parser = argparse.ArgumentParser(description='Clear all data from all database tables')
    parser.add_argument('--confirm', action='store_true', 
                       help='Skip confirmation prompt')
    
    args = parser.parse_args()
    
    # Show warning and get confirmation
    if not args.confirm:
        print("⚠️  WARNING: COMPLETE DATABASE DATA CLEARING")
        print("=" * 50)
        print("This will DELETE ALL DATA from ALL TABLES in the database!")
        print("This action CANNOT be undone!")
        print("")
        print("Tables that will be affected:")
        print("• All product data")
        print("• All user data")
        print("• All order data")
        print("• All analytics data")
        print("• All system data")
        print("")
        response = input("Are you absolutely sure? Type 'DELETE ALL DATA' to confirm: ")
        
        if response != 'DELETE ALL DATA':
            print("❌ Operation cancelled for safety.")
            return False
    
    # Perform the clearing
    success = clear_all_database_data()
    
    if success:
        print("\n🔄 NEXT STEPS:")
        print("-" * 20)
        print("1. Run: python seed_database.py --products=100")
        print("2. Run: python reindex_products.py")
        print("3. Test the application")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
