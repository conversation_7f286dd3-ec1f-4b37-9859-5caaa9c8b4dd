#!/usr/bin/env python3
"""
Final Comprehensive Database Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
import logging
from sqlalchemy import text
from datetime import datetime
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_operations():
    """Test basic database operations"""
    
    print("🧪 TESTING DATABASE OPERATIONS")
    print("="*80)
    
    with app.app_context():
        try:
            # Test 1: Basic connection
            result = db.session.execute(text("SELECT 1 as test"))
            assert result.scalar() == 1
            print("✅ Database connection: WORKING")
            
            # Test 2: Table count
            result = db.session.execute(text("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()"))
            table_count = result.scalar()
            print(f"✅ Total tables: {table_count}")
            
            # Test 3: Test critical tables exist and have data
            critical_tables = ['user', 'product', 'orders', 'cart_item', 'shipping_carriers']
            
            for table in critical_tables:
                try:
                    result = db.session.execute(text(f"SELECT COUNT(*) FROM `{table}`"))
                    count = result.scalar()
                    print(f"✅ Table '{table}': {count} records")
                except Exception as e:
                    print(f"❌ Table '{table}': ERROR - {e}")
            
            # Test 4: Test foreign key relationships
            print(f"\n🔗 Testing Foreign Key Relationships:")
            
            # Check order_item -> orders relationship
            result = db.session.execute(text("""
                SELECT COUNT(*) FROM order_item oi 
                LEFT JOIN orders o ON oi.order_id = o.id 
                WHERE o.id IS NULL AND oi.order_id IS NOT NULL
            """))
            orphaned_order_items = result.scalar()
            
            if orphaned_order_items == 0:
                print("✅ Order items -> Orders: No orphaned records")
            else:
                print(f"⚠️  Order items -> Orders: {orphaned_order_items} orphaned records")
            
            # Check cart_item -> user relationship
            result = db.session.execute(text("""
                SELECT COUNT(*) FROM cart_item ci 
                LEFT JOIN user u ON ci.user_id = u.id 
                WHERE u.id IS NULL AND ci.user_id IS NOT NULL
            """))
            orphaned_cart_items = result.scalar()
            
            if orphaned_cart_items == 0:
                print("✅ Cart items -> Users: No orphaned records")
            else:
                print(f"⚠️  Cart items -> Users: {orphaned_cart_items} orphaned records")
            
            # Test 5: Test data integrity
            print(f"\n🛡️  Testing Data Integrity:")
            
            # Check for negative stock quantities
            result = db.session.execute(text("SELECT COUNT(*) FROM product WHERE stock_quantity < 0"))
            negative_stock = result.scalar()
            
            if negative_stock == 0:
                print("✅ Product stock quantities: All positive")
            else:
                print(f"⚠️  Product stock quantities: {negative_stock} negative values")
            
            # Check for invalid prices
            result = db.session.execute(text("SELECT COUNT(*) FROM product WHERE price <= 0"))
            invalid_prices = result.scalar()
            
            if invalid_prices == 0:
                print("✅ Product prices: All valid")
            else:
                print(f"⚠️  Product prices: {invalid_prices} invalid values")
            
            return True
            
        except Exception as e:
            print(f"❌ Database test failed: {e}")
            return False

def test_model_operations():
    """Test SQLAlchemy model operations"""
    
    print(f"\n🏗️  TESTING MODEL OPERATIONS")
    print("="*80)
    
    with app.app_context():
        try:
            # Import models
            from app import User, Product, CartItem, Order, Seller
            
            # Test 1: Query operations
            user_count = User.query.count()
            product_count = Product.query.count()
            order_count = Order.query.count()
            
            print(f"✅ Model queries working:")
            print(f"  - Users: {user_count}")
            print(f"  - Products: {product_count}")
            print(f"  - Orders: {order_count}")
            
            # Test 2: Relationship queries
            if user_count > 0:
                user = User.query.first()
                cart_items = user.cart_items
                print(f"✅ User relationships: User has {len(cart_items)} cart items")
            
            if product_count > 0:
                product = Product.query.first()
                print(f"✅ Product model: {product.name} - ₹{product.price}")
            
            # Test 3: Create and rollback test (don't commit)
            test_user = User(
                username=f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                email=f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
                password="test_password"
            )
            
            db.session.add(test_user)
            db.session.flush()  # Get ID without committing
            
            test_user_id = test_user.id
            print(f"✅ Model creation: Test user created with ID {test_user_id}")
            
            db.session.rollback()  # Rollback the test
            print("✅ Transaction rollback: Test data cleaned up")
            
            return True
            
        except Exception as e:
            print(f"❌ Model test failed: {e}")
            db.session.rollback()
            return False

def test_api_integration():
    """Test that database works with API endpoints"""
    
    print(f"\n🌐 TESTING API INTEGRATION")
    print("="*80)
    
    with app.app_context():
        try:
            # Test that we can query data for API responses
            from app import Product, User, ShippingCarrier
            
            # Test product API data
            products = Product.query.limit(5).all()
            print(f"✅ Product API data: {len(products)} products available")
            
            # Test user API data
            users = User.query.limit(5).all()
            print(f"✅ User API data: {len(users)} users available")
            
            # Test shipping carriers (we added these earlier)
            carriers = ShippingCarrier.query.all()
            print(f"✅ Shipping API data: {len(carriers)} carriers available")
            
            # Test that we can serialize data to JSON
            if products:
                product_data = {
                    'id': products[0].id,
                    'name': products[0].name,
                    'price': products[0].price,
                    'stock_quantity': products[0].stock_quantity
                }
                json_data = json.dumps(product_data)
                print(f"✅ JSON serialization: Product data serializable")
            
            return True
            
        except Exception as e:
            print(f"❌ API integration test failed: {e}")
            return False

def generate_database_health_report():
    """Generate a comprehensive database health report"""
    
    print(f"\n📊 DATABASE HEALTH REPORT")
    print("="*80)
    
    with app.app_context():
        try:
            # Get database statistics
            result = db.session.execute(text("""
                SELECT 
                    table_name,
                    table_rows,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb'
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                ORDER BY (data_length + index_length) DESC
                LIMIT 10
            """))
            
            print("🏆 Top 10 Largest Tables:")
            for row in result:
                table_name, table_rows, size_mb = row
                print(f"  {table_name:<25} | {table_rows:>8} rows | {size_mb:>8} MB")
            
            # Get total database size
            result = db.session.execute(text("""
                SELECT 
                    ROUND(SUM((data_length + index_length) / 1024 / 1024), 2) AS 'total_size_mb'
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            """))
            
            total_size = result.scalar()
            print(f"\n💾 Total Database Size: {total_size} MB")
            
            # Get foreign key constraint count
            result = db.session.execute(text("""
                SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE 
                WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
                AND REFERENCED_TABLE_NAME IS NOT NULL
            """))
            
            fk_count = result.scalar()
            print(f"🔗 Foreign Key Constraints: {fk_count}")
            
            # Get index count
            result = db.session.execute(text("""
                SELECT COUNT(*) FROM information_schema.statistics 
                WHERE table_schema = DATABASE()
            """))
            
            index_count = result.scalar()
            print(f"📇 Total Indexes: {index_count}")
            
            return True
            
        except Exception as e:
            print(f"❌ Health report generation failed: {e}")
            return False

def main():
    print("🚀 Starting Final Database Test Suite...")
    print(f"⏰ Test started at: {datetime.now()}")
    
    # Run all tests
    tests_passed = 0
    total_tests = 4
    
    if test_database_operations():
        tests_passed += 1
    
    if test_model_operations():
        tests_passed += 1
    
    if test_api_integration():
        tests_passed += 1
    
    if generate_database_health_report():
        tests_passed += 1
    
    # Final summary
    print(f"\n🎯 FINAL TEST RESULTS")
    print("="*80)
    print(f"✅ Tests Passed: {tests_passed}/{total_tests}")
    print(f"📊 Success Rate: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! Database is in excellent condition!")
    else:
        print("⚠️  Some tests failed. Review the output above for details.")
    
    print(f"⏰ Test completed at: {datetime.now()}")

if __name__ == '__main__':
    main()
