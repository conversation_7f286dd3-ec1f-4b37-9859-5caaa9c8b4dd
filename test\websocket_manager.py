"""
DEPRECATED: FastAPI WebSocket Manager - USE FLASK-SOCKE<PERSON>O INSTEAD
================================================================

This file uses FastAPI WebSocket which is incompatible with the Flask application.
Use flask_socketio_manager.py for Flask-SocketIO implementation instead.

Issues:
- FastAPI WebSocket not compatible with Flask app
- Cannot be served by Flask/Waitress server
- Requires separate FastAPI server

Status: DEPRECATED - USE flask_socketio_manager.py INSTEAD
Alternative: flask_socketio_manager.py provides all the same functionality

Date Deprecated: 2025-07-13
"""

"""
WebSocket Manager for Real-time Features
Handles WebSocket connections, events, and real-time updates
"""

import asyncio
import json
import logging
from typing import Dict, Set, Any, Optional
from datetime import datetime
import redis
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
# from database import get_db  # Fixed: Use app context
# from models import User, Product, CartItem, Order  # Fixed: Import from app

logger = logging.getLogger(__name__)

class ConnectionManager:
    """Manages WebSocket connections and broadcasting"""
    
    def __init__(self):
        # Active connections by user_id
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Guest connections by session_id
        self.guest_connections: Dict[str, Set[WebSocket]] = {}
        # All connections for broadcasting
        self.all_connections: Set[WebSocket] = set()
        # Redis client for pub/sub
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
    async def connect(self, websocket: WebSocket, user_id: Optional[str] = None, session_id: Optional[str] = None):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.all_connections.add(websocket)
        
        if user_id:
            if user_id not in self.active_connections:
                self.active_connections[user_id] = set()
            self.active_connections[user_id].add(websocket)
            logger.info(f"User {user_id} connected via WebSocket")
        elif session_id:
            if session_id not in self.guest_connections:
                self.guest_connections[session_id] = set()
            self.guest_connections[session_id].add(websocket)
            logger.info(f"Guest {session_id} connected via WebSocket")
            
        # Send connection confirmation
        await self.send_personal_message({
            "type": "connection_established",
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "session_id": session_id
        }, websocket)
        
    def disconnect(self, websocket: WebSocket, user_id: Optional[str] = None, session_id: Optional[str] = None):
        """Remove a WebSocket connection"""
        self.all_connections.discard(websocket)
        
        if user_id and user_id in self.active_connections:
            self.active_connections[user_id].discard(websocket)
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
            logger.info(f"User {user_id} disconnected from WebSocket")
            
        if session_id and session_id in self.guest_connections:
            self.guest_connections[session_id].discard(websocket)
            if not self.guest_connections[session_id]:
                del self.guest_connections[session_id]
            logger.info(f"Guest {session_id} disconnected from WebSocket")
            
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Send message to a specific WebSocket connection"""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            
    async def send_to_user(self, message: Dict[str, Any], user_id: str):
        """Send message to all connections of a specific user"""
        if user_id in self.active_connections:
            disconnected = []
            for websocket in self.active_connections[user_id].copy():
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error sending to user {user_id}: {e}")
                    disconnected.append(websocket)
                    
            # Clean up disconnected websockets
            for ws in disconnected:
                self.active_connections[user_id].discard(ws)
                self.all_connections.discard(ws)
                
    async def send_to_guest(self, message: Dict[str, Any], session_id: str):
        """Send message to all connections of a specific guest session"""
        if session_id in self.guest_connections:
            disconnected = []
            for websocket in self.guest_connections[session_id].copy():
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error sending to guest {session_id}: {e}")
                    disconnected.append(websocket)
                    
            # Clean up disconnected websockets
            for ws in disconnected:
                self.guest_connections[session_id].discard(ws)
                self.all_connections.discard(ws)
                
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast message to all connected clients"""
        disconnected = []
        for websocket in self.all_connections.copy():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                disconnected.append(websocket)
                
        # Clean up disconnected websockets
        for ws in disconnected:
            self.all_connections.discard(ws)
            
    async def broadcast_to_users(self, message: Dict[str, Any], user_ids: Set[str]):
        """Broadcast message to specific users"""
        for user_id in user_ids:
            await self.send_to_user(message, user_id)

# Global connection manager instance
manager = ConnectionManager()

class WebSocketEventHandler:
    """Handles different types of WebSocket events"""
    
    def __init__(self, db: Session):
        self.db = db
        
    async def handle_cart_update(self, user_id: str, session_id: str, data: Dict[str, Any]):
        """Handle cart update events"""
        try:
            message = {
                "type": "cart_updated",
                "timestamp": datetime.now().isoformat(),
                "data": data
            }
            
            if user_id:
                await manager.send_to_user(message, user_id)
            elif session_id:
                await manager.send_to_guest(message, session_id)
                
        except Exception as e:
            logger.error(f"Error handling cart update: {e}")
            
    async def handle_inventory_update(self, product_id: int, new_stock: int, variant_id: int = None, old_stock: int = None):
        """Handle inventory update events"""
        try:
            message = {
                "type": "inventory_updated",
                "timestamp": datetime.now().isoformat(),
                "product_id": product_id,
                "variant_id": variant_id,
                "new_quantity": new_stock,
                "old_quantity": old_stock,
                "quantity_change": (new_stock - old_stock) if old_stock is not None else 0,
                "in_stock": new_stock > 0
            }

            # Broadcast to all users
            await manager.broadcast(message)

            # Also publish to Redis for other services
            await self.publish_to_redis("inventory_updates", message)

        except Exception as e:
            logger.error(f"Error handling inventory update: {e}")

    async def handle_sync_status_change(self, channel_id: int, channel_name: str, status: str, product_id: int = None, error: str = None):
        """Handle sync status change events"""
        try:
            message = {
                "type": "sync_status_changed",
                "timestamp": datetime.now().isoformat(),
                "channel_id": channel_id,
                "channel_name": channel_name,
                "status": status,
                "product_id": product_id,
                "error": error
            }

            # Broadcast to admin users only
            await self.broadcast_to_admins(message)

            # Publish to Redis
            await self.publish_to_redis("sync_status_updates", message)

        except Exception as e:
            logger.error(f"Error handling sync status change: {e}")

    async def handle_conflict_detected(self, conflict_id: int, product_id: int, product_name: str, conflict_type: str, priority: str):
        """Handle inventory conflict detection"""
        try:
            message = {
                "type": "conflict_detected",
                "timestamp": datetime.now().isoformat(),
                "conflict_id": conflict_id,
                "product_id": product_id,
                "product_name": product_name,
                "conflict_type": conflict_type,
                "priority": priority
            }

            # Broadcast to admin users only
            await self.broadcast_to_admins(message)

            # Publish to Redis
            await self.publish_to_redis("conflict_alerts", message)

        except Exception as e:
            logger.error(f"Error handling conflict detection: {e}")

    async def handle_conflict_resolved(self, conflict_id: int, resolved_quantity: int, strategy: str):
        """Handle inventory conflict resolution"""
        try:
            message = {
                "type": "conflict_resolved",
                "timestamp": datetime.now().isoformat(),
                "conflict_id": conflict_id,
                "resolved_quantity": resolved_quantity,
                "strategy": strategy
            }

            # Broadcast to admin users only
            await self.broadcast_to_admins(message)

            # Publish to Redis
            await self.publish_to_redis("conflict_resolutions", message)

        except Exception as e:
            logger.error(f"Error handling conflict resolution: {e}")

    async def broadcast_to_admins(self, message: dict):
        """Broadcast message to admin users only"""
        try:
            admin_connections = []
            for connection_id, connection_info in self.active_connections.items():
                if connection_info.get('is_admin', False):
                    admin_connections.append(connection_id)

            if admin_connections:
                await self.broadcast_to_connections(message, admin_connections)

        except Exception as e:
            logger.error(f"Error broadcasting to admins: {e}")

    async def broadcast_to_connections(self, message: dict, connection_ids: list):
        """Broadcast message to specific connections"""
        try:
            message_str = json.dumps(message)

            for connection_id in connection_ids:
                if connection_id in self.active_connections:
                    websocket = self.active_connections[connection_id]['websocket']
                    try:
                        await websocket.send_text(message_str)
                    except Exception as e:
                        logger.error(f"Error sending message to connection {connection_id}: {e}")
                        # Remove failed connection
                        await self.disconnect(websocket)

        except Exception as e:
            logger.error(f"Error broadcasting to connections: {e}")
            
    async def handle_order_status_update(self, user_id: str, order_id: int, status: str):
        """Handle order status update events"""
        try:
            message = {
                "type": "order_status_updated",
                "timestamp": datetime.now().isoformat(),
                "order_id": order_id,
                "status": status
            }
            
            await manager.send_to_user(message, user_id)
            
        except Exception as e:
            logger.error(f"Error handling order status update: {e}")
            
    async def handle_price_update(self, product_id: int, new_price: float, old_price: float):
        """Handle price update events"""
        try:
            message = {
                "type": "price_updated",
                "timestamp": datetime.now().isoformat(),
                "product_id": product_id,
                "new_price": new_price,
                "old_price": old_price,
                "discount_percentage": round(((old_price - new_price) / old_price) * 100, 2) if old_price > 0 else 0
            }
            
            # Broadcast to all users
            await manager.broadcast(message)
            
        except Exception as e:
            logger.error(f"Error handling price update: {e}")
            
    async def handle_notification(self, user_id: str, notification_data: Dict[str, Any]):
        """Handle user notification events"""
        try:
            message = {
                "type": "notification",
                "timestamp": datetime.now().isoformat(),
                "data": notification_data
            }
            
            await manager.send_to_user(message, user_id)
            
        except Exception as e:
            logger.error(f"Error handling notification: {e}")
            
    async def publish_to_redis(self, channel: str, message: Dict[str, Any]):
        """Publish message to Redis pub/sub"""
        try:
            manager.redis_client.publish(channel, json.dumps(message))
        except Exception as e:
            logger.error(f"Error publishing to Redis: {e}")

# Event handler functions for external use
async def notify_cart_update(user_id: str = None, session_id: str = None, cart_data: Dict[str, Any] = None):
    """Notify clients about cart updates"""
    # db = next(get_db())  # Fixed: Use app context
    handler = WebSocketEventHandler(db)
    await handler.handle_cart_update(user_id, session_id, cart_data)
    
async def notify_inventory_update(product_id: int, new_stock: int):
    """Notify clients about inventory updates"""
    # db = next(get_db())  # Fixed: Use app context
    handler = WebSocketEventHandler(db)
    await handler.handle_inventory_update(product_id, new_stock)
    
async def notify_order_status_update(user_id: str, order_id: int, status: str):
    """Notify clients about order status updates"""
    # db = next(get_db())  # Fixed: Use app context
    handler = WebSocketEventHandler(db)
    await handler.handle_order_status_update(user_id, order_id, status)
    
async def notify_price_update(product_id: int, new_price: float, old_price: float):
    """Notify clients about price updates"""
    # db = next(get_db())  # Fixed: Use app context
    handler = WebSocketEventHandler(db)
    await handler.handle_price_update(product_id, new_price, old_price)
    
async def send_user_notification(user_id: str, notification_data: Dict[str, Any]):
    """Send notification to specific user"""
    # db = next(get_db())  # Fixed: Use app context
    handler = WebSocketEventHandler(db)
    await handler.handle_notification(user_id, notification_data)

# WebSocket endpoint handler
async def websocket_endpoint(websocket: WebSocket, user_id: str = None, session_id: str = None):
    """Main WebSocket endpoint handler"""
    await manager.connect(websocket, user_id, session_id)
    # db = next(get_db())  # Fixed: Use app context
    event_handler = WebSocketEventHandler(db)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle different message types
            message_type = message.get("type")
            
            if message_type == "ping":
                await manager.send_personal_message({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }, websocket)
                
            elif message_type == "subscribe":
                # Handle subscription to specific events
                events = message.get("events", [])
                await manager.send_personal_message({
                    "type": "subscribed",
                    "events": events,
                    "timestamp": datetime.now().isoformat()
                }, websocket)
                
            elif message_type == "heartbeat":
                await manager.send_personal_message({
                    "type": "heartbeat_ack",
                    "timestamp": datetime.now().isoformat()
                }, websocket)
                
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id, session_id)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket, user_id, session_id)
