#!/usr/bin/env python3
"""
Quick Endpoint Test Runner
=========================

Simple script to run the comprehensive endpoint tests with different configurations.
"""

import subprocess
import sys
import os
import time
from datetime import datetime

def check_backend_status():
    """Check if the backend is running"""
    try:
        import requests
        response = requests.get("http://localhost:5000/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """Start the backend if not running"""
    if not check_backend_status():
        print("🚀 Starting backend server...")
        print("Please start your Flask backend manually and then run this script again.")
        print("Command: python app.py")
        return False
    return True

def run_quick_test():
    """Run a quick test of critical endpoints"""
    print("⚡ Running quick endpoint test...")
    
    # Create a minimal test script for critical endpoints
    quick_test_script = """
import requests
import json

BASE_URL = "http://localhost:5000"
critical_endpoints = [
    ("GET", "/api/health", "Health check"),
    ("GET", "/api/products", "Products list"),
    ("GET", "/api/categories", "Categories"),
    ("GET", "/api/search", "Search"),
    ("GET", "/api/recommendations", "Recommendations"),
]

print("🔍 Testing critical endpoints...")
results = []
for method, path, desc in critical_endpoints:
    try:
        response = requests.get(f"{BASE_URL}{path}", timeout=10)
        status = "✅" if response.status_code in [200, 201] else "❌"
        results.append(f"{status} {method} {path} ({response.status_code}) - {desc}")
    except Exception as e:
        results.append(f"❌ {method} {path} (ERROR) - {desc}: {str(e)}")

for result in results:
    print(result)
"""
    
    exec(quick_test_script)

def run_full_test():
    """Run the full comprehensive test"""
    print("🧪 Running comprehensive endpoint test...")
    
    try:
        # Run the main test script
        result = subprocess.run([sys.executable, "test_all_endpoints.py"], 
                              capture_output=True, text=True, timeout=300)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("❌ Test timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def main():
    """Main function"""
    print("🚀 ALLORA API ENDPOINT TEST RUNNER")
    print("=" * 50)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check if backend is running
    if not start_backend():
        sys.exit(1)
    
    print("✅ Backend is running")
    print()
    
    # Ask user what type of test to run
    print("Select test type:")
    print("1. Quick test (critical endpoints only)")
    print("2. Full comprehensive test (all endpoints)")
    print("3. Both")
    
    try:
        choice = input("\nEnter choice (1-3): ").strip()
    except KeyboardInterrupt:
        print("\n👋 Test cancelled by user")
        sys.exit(0)
    
    success = True
    
    if choice in ["1", "3"]:
        print("\n" + "="*50)
        run_quick_test()
        print("="*50)
    
    if choice in ["2", "3"]:
        print("\n" + "="*50)
        success = run_full_test()
        print("="*50)
    
    if choice not in ["1", "2", "3"]:
        print("❌ Invalid choice. Running quick test...")
        run_quick_test()
    
    print(f"\n🏁 Test completed: {'✅ SUCCESS' if success else '❌ SOME FAILURES'}")

if __name__ == "__main__":
    main()
